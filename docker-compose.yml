version: '3.8'

services:
  frontend:
    build:
      context: .
      args:
        VITE_SUPABASE_URL: ${VITE_SUPABASE_URL}
        VITE_SUPABASE_ANON_KEY: ${VITE_SUPABASE_ANON_KEY}
        BREVO_API_KEY: ${BREVO_API_KEY}
        VITE_APP_URL: ${VITE_APP_URL}
    container_name: app-meddoc
    ports:
      - "4000:80"
    restart: unless-stopped
    environment:
      - VITE_SUPABASE_URL=${VITE_SUPABASE_URL}
      - VITE_SUPABASE_ANON_KEY=${VITE_SUPABASE_ANON_KEY}
      - BREVO_API_KEY=${BREVO_API_KEY}
      - VITE_APP_URL=${VITE_APP_URL}
