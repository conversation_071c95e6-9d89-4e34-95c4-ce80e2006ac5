# Correction du Problème de Révocation de Session sur les Routes DASH

## 🔍 Problème Identifié

Lors du rafraîchissement de page sur les routes DASH, la session était systématiquement révoquée, forçant l'utilisateur à se reconnecter.

## 🕵️ Diagnostic

### Cause Racine
Une **incohérence dans l'injection de dépendances** pour les utilisateurs DASH dans le système de restauration des données :

1. **`DashInformationProvider`** attendait un `IGetDashByUserIdUsecase`
2. **`UserInformationProviderFactory`** lui passait un `IGetDashByIdRepository`

Cette incompatibilité causait une erreur lors de l'exécution de `UserDataCollector.execute()`, qui tentait de restaurer les données utilisateur après un rafraîchissement de page.

### Séquence du Problème
1. Utilisateur navigue vers une route DASH → ✅ Fonctionne (données déjà en mémoire)
2. Utilisateur rafraîchit la page → ❌ Échec
3. `useRestoreAuth` hook s'exécute → appelle `restoreData()`
4. `UserDataCollector.execute()` → appelle `userInformationProvider.getUserInformation()`
5. **ERREUR** : Type mismatch entre usecase attendu et repository fourni
6. Catch block retourne `NO_USER_RETURN_VAL` → session considérée comme invalide
7. Utilisateur redirigé vers la page de connexion

## 🔧 Solution Implémentée

### Modifications Apportées

#### 1. `src/application/slices/auth/authSlice.ts`
```typescript
// Ajout des imports nécessaires
import GetDashByUserIdRepository from "@/infrastructure/repositories/dash/GetDashByUserIdRepository.ts";
import GetDashByUserIdUsecase from "@/domain/usecases/dash/GetDashByUserIdUsecase.ts";

// Ajout du repository dans authRepositories
const authRepositories = {
  // ... autres repositories
  getDashByUserIdRepository: new GetDashByUserIdRepository(),
};

// Création du usecase DASH
const getDashByUserIdUsecase = new GetDashByUserIdUsecase(
  authRepositories.getDashByUserIdRepository
);

// Correction de l'injection dans la factory
const userInformationProviderFactory = new UserInformationProviderFactory(
  patientRepositories.getByUserId,
  professionalRepository,
  adminRepositories.getByUserId,
  getDashByUserIdUsecase // ✅ Usecase au lieu du repository
);
```

#### 2. `src/domain/services/UserInformationProviderFactory.ts`
```typescript
// Correction de l'import
import { IGetDashByUserIdUsecase } from "../interfaces/usecases/dash/IGetDashByUserIdUsecase";

// Correction du constructeur
constructor(
  private readonly patientRepository: IGetPatientByUserIdRepository,
  private readonly professionalRepository: IProfessionalRepository,
  private readonly adminRepository: IGetAdminByUserIdRepository,
  private readonly getDashByUserIdUsecase: IGetDashByUserIdUsecase // ✅ Type correct
) {}

// Correction de l'injection
[utilisateurs_role_enum.DASH]: () =>
  new DashInformationProvider(this.getDashByUserIdUsecase), // ✅ Usecase fourni
```

## ✅ Résultat

- **Avant** : Révocation de session systématique sur rafraîchissement des routes DASH
- **Après** : Session maintenue correctement, restauration des données utilisateur fonctionnelle

## 🧪 Test de Validation

Pour tester la correction :

1. Se connecter avec un compte DASH
2. Naviguer vers une route DASH (ex: `/commune-urbaine-antananarivo/dash/dashboard`)
3. Rafraîchir la page (F5 ou Ctrl+R)
4. ✅ L'utilisateur reste connecté et les données sont restaurées

## 📝 Notes Techniques

- La correction respecte l'architecture DDD existante
- Aucun changement breaking pour les autres types d'utilisateurs
- La solution suit le pattern établi pour les autres providers (Patient, Professional, Admin)
- Les interfaces et types sont maintenant cohérents dans toute la chaîne d'injection

## 🔄 Architecture de Restauration des Données

```mermaid
graph TD
    A[Page Refresh] --> B[useRestoreAuth Hook]
    B --> C[restoreData Action]
    C --> D[UserDataCollector.execute]
    D --> E[getAuthenticatedUser]
    E --> F[getUserByEmail]
    F --> G[UserInformationProviderFactory]
    G --> H[DashInformationProvider]
    H --> I[GetDashByUserIdUsecase]
    I --> J[GetDashByUserIdRepository]
    J --> K[Supabase Query]
    K --> L[User Data Restored ✅]
```

Cette correction garantit que la chaîne de restauration fonctionne correctement pour tous les types d'utilisateurs, y compris les utilisateurs DASH.
