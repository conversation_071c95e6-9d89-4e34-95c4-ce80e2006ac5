import { Employer } from "@/domain/models/Employer";
import { supabase } from "@/infrastructure/supabase/supabase";
import { EMPLOYER_TABLE_NAME } from "./constants";
import { IGetEmployersByUserIdRepository } from "@/domain/interfaces/repositories/employer/IGetEmployersByUserIdRepository";

export class GetEmployersByUserIdRepository
  implements IGetEmployersByUserIdRepository
{
  async execute(id_utilisateur: number): Promise<Employer[]> {
    const { data, error } = await supabase
      .from(EMPLOYER_TABLE_NAME)
      .select("*")
      .eq("id_utilisateur", id_utilisateur);
    if (error) throw error;
    return (data as Employer[]) || [];
  }
}
