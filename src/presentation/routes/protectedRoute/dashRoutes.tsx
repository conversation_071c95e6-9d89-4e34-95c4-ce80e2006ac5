import Stock from "@/presentation/components/features/professional/stock/Stock";
import { DashRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { lazy } from "react";

const Dashboard = lazy(
  () => import("@/presentation/pages/dash/dashboard/Dashboard")
);

const HelpPage = lazy(() => import("@/presentation/pages/shared/help/Helps"));

const Employer = lazy(
  () => import("@/presentation/pages/dash/employer/MyEmployees")
);

const dashRoutes = [
  { path: `/${DashRoutesNavigation.DASHBOARD}`, element: <Dashboard /> },
  {
    path: `/${DashRoutesNavigation.EMPLOYER}`,
    element: <Employer />,
  },
  {
    path: `/${DashRoutesNavigation.STOCK}`,
    element: <Stock />,
  },
  { path: `/${DashRoutesNavigation.HELP}`, element: <HelpPage /> },
];

export default dashRoutes;
