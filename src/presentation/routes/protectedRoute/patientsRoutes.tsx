import { PatientRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { lazy } from "react";

const Dashboard = lazy(
  () => import("@/presentation/pages/patient/dashboard/Dashboard"),
);

const FindDoctorPage = lazy(
  () => import("@/presentation/pages/patient/findDoctor/FindDoctor"),
);

const AppointmentsPage = lazy(
  () => import("@/presentation/pages/patient/appointments/Appointments"),
);

const MedicalActivityPage = lazy(
  () => import("@/presentation/pages/patient/medicalActivity/MedicalActivity"),
);

const FamiliesPage = lazy(
  () => import("@/presentation/pages/patient/families/Families"),
);

const Messages = lazy(
  () => import("@/presentation/pages/shared/messages/Messages"),
);

const HelpPage = lazy(() => import("@/presentation/pages/shared/help/Helps"));

const ProfilePage = lazy(
  () => import("@/presentation/pages/shared/profile/Profile"),
);

const patientRoutes = [
  {
    path: `/${PatientRoutesNavigation.DASHBOARD}`,
    element: <Dashboard />,
  },
  {
    path: `/${PatientRoutesNavigation.FIND_DOCTOR}`,
    element: <FindDoctorPage />,
  },
  {
    path: `/${PatientRoutesNavigation.APPOINTMENTS}`,
    element: <AppointmentsPage />,
  },
  {
    path: `/${PatientRoutesNavigation.MEDICAL_ACTIVITY}`,
    element: <MedicalActivityPage />,
  },
  {
    path: `/${PatientRoutesNavigation.FAMILIES}`,
    element: <FamiliesPage />,
  },
  {
    path: `/${PatientRoutesNavigation.MESSAGES}`,
    element: <Messages />,
  },

  {
    path: `/${PatientRoutesNavigation.HELP}`,
    element: <HelpPage />,
  },
  {
    path: `/${PatientRoutesNavigation.PROFILE}`,
    element: <ProfilePage />,
  },
];

export default patientRoutes;
