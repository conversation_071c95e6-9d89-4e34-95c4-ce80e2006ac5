import { ProfessionalRoutesNavigations } from "@/shared/constants/AppRoutesNavigation";
import { lazy } from "react";

const Dashboard = lazy(
  () => import("@/presentation/pages/professional/dashboard/Dashboard")
);

const AgendaPage = lazy(
  () => import("@/presentation/pages/professional/agenda/Agenda")
);

const AppointmentsPage = lazy(
  () => import("@/presentation/pages/professional/appointments/Appointments")
);

const PatientPage = lazy(
  () => import("@/presentation/pages/shared/patients/MyPatients")
);

const PatientInfo = lazy(
  () => import("@/presentation/pages/shared/patients/PatientInfo")
);

const Messages = lazy(
  () => import("@/presentation/pages/shared/messages/Messages")
);

const FacturationPage = lazy(
  () => import("@/presentation/pages/professional/facturation/Facturation")
);

const HelpPage = lazy(
  () => import("@/presentation/pages/professional/facturation/Facturation")
);

const ProfessionalStock = lazy(
  () => import("@/presentation/pages/professional/stock/ProfessionalStock")
);

const EmployeesPage = lazy(
  () => import("@/presentation/pages/dash/employer/MyEmployees")
);

const EmployedInfo = lazy(
  () => import("@/presentation/pages/shared/patients/PatientInfo")
);

const ProfilePage = lazy(
  () => import("@/presentation/pages/shared/profile/Profile")
);

const professionalRoutes = [
  {
    path: `/${ProfessionalRoutesNavigations.DASHBOARD}`,
    element: <Dashboard />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.AGENDA}`,
    element: <AgendaPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.APPOINTMENTS}`,
    element: <AppointmentsPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS}`,
    element: <PatientPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS_ACTIF}`,
    element: <PatientPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS_DECEDE}`,
    element: <PatientPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS_SUPPRIMER}`,
    element: <PatientPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS_PAGE}`,
    element: <PatientInfo />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MESSAGES}`,
    element: <Messages />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.FACTURATION}`,
    element: <FacturationPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.HELP}`,
    element: <HelpPage />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.STOCK}`,
    element: <ProfessionalStock />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.MANAGE_EMPLOYED_PAGE}`,
    element: <EmployedInfo />,
  },
  {
    path: `/${ProfessionalRoutesNavigations.PROFILE}`,
    element: <ProfilePage />,
  },
];

export default professionalRoutes;
