import { useCallback } from "react";
import { FieldErrors, UseFormTrigger, UseFormGetValues } from "react-hook-form";
import { useConsultationStepValidation } from "./useConsultationStepValidation";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";
import { getSelectedTimeSlot } from "./getSelectedTimeSlot";
import { DataStepConsultation } from "./DataStepConsultation";
import { saveToLocalStorage } from "./saveToLocalStorage";
import { CONSULTATION_STEP_DATA } from "./constant";

interface UseConsultationStepNavigationProps {
  activeStep: number;
  handleNext: (count: number) => void;
  handleBack: (count: number) => void;
  getValues: UseFormGetValues<ConsultationStepFormData>;
  trigger: UseFormTrigger<ConsultationStepFormData>;
  errors: FieldErrors<ConsultationStepFormData>;
}

interface UseConsultationStepNavigationReturn {
  handleNextStep: (count: number) => Promise<void>;
  handlePreviousStep: (count: number) => void;
  canNavigateToStep: (targetStep: number) => Promise<boolean>;
}

/**
 * Hook personnalisé pour gérer la navigation du formulaire d'inscription patient
 *
 * Responsabilités :
 * - Navigation vers l'étape suivante avec validation
 * - Navigation vers l'étape précédente
 * - Vérification des permissions de navigation
 * - Intégration avec la validation
 */
export const useConsultationStepNavigation = ({
  activeStep,
  handleNext,
  handleBack,
  getValues,
  trigger,
  errors,
}: UseConsultationStepNavigationProps): UseConsultationStepNavigationReturn => {
  const { validateCurrentStep, showValidationError } =
    useConsultationStepValidation({
      trigger,
      errors,
    });

  /**
   * Gère la navigation vers l'étape suivante avec validation
   */
  const handleNextStep = useCallback(
    async (count: number): Promise<void> => {
      const isCurrentStepValid = await validateCurrentStep(activeStep);

      if (isCurrentStepValid) {
        if (activeStep === 0) {
          const data = getSelectedTimeSlot();
          const typeDeConsultation = getValues("categorie");
          const speciality = getValues("speciality");
          const consultationMotif = getValues("consultationMotif");
          const newData: DataStepConsultation = {
            ...data,
            step1Data: {
              motifDeConsultation: consultationMotif,
              specialiteMedicale: speciality,
              typeDeConsultation: typeDeConsultation,
            },
          };
          saveToLocalStorage(CONSULTATION_STEP_DATA, newData);
        }
        handleNext(count);
      } else {
        showValidationError(activeStep);
      }
    },
    [activeStep, validateCurrentStep, handleNext, showValidationError]
  );

  /**
   * Gère la navigation vers l'étape précédente
   */
  const handlePreviousStep = useCallback(
    (count: number): void => {
      handleBack(count);
    },
    [handleBack]
  );

  /**
   * Vérifie si on peut naviguer vers une étape spécifique
   * Utile pour la navigation directe via le stepper
   */
  const canNavigateToStep = useCallback(
    async (targetStep: number): Promise<boolean> => {
      // On peut toujours revenir en arrière
      if (targetStep <= activeStep) {
        return true;
      }

      // Pour aller en avant, il faut valider toutes les étapes intermédiaires
      for (let step = activeStep; step < targetStep; step++) {
        const isStepValid = await validateCurrentStep(step);
        if (!isStepValid) {
          showValidationError(step);
          return false;
        }
      }

      return true;
    },
    [activeStep, validateCurrentStep, showValidationError]
  );

  return {
    handleNextStep,
    handlePreviousStep,
    canNavigateToStep,
  };
};
