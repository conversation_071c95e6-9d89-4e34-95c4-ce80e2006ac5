import { useState } from "react";
import { useToast } from "../use-toast";
import {
  Control,
  FieldErrors,
  useForm,
  UseFormGetValues,
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormSetValue,
  UseFormTrigger,
  UseFormWatch,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { SupabaseError } from "@/infrastructure/supabase/supabaseError";
import {
  ConsultationStepFormData,
  consultationStepShema,
} from "@/shared/schemas/ConsultationStepShema";
import { formatDataReturn } from "./formatDataReturn";
import { RendezVous } from "@/domain/models";
import { createAppointment } from "@/application/slices/professionnal/appointmentSlice";
import { createProche } from "@/application/slices/patient/prochePatientSlice";
import { AppDispatch } from "@/store";
import { useDispatch } from "react-redux";
interface UseConsultationStepReturn {
  isLoading: boolean;
  control: Control<ConsultationStepFormData>;
  formState: { errors: FieldErrors<ConsultationStepFormData> };
  confirmeAppointment: (data: formatDataReturn) => Promise<RendezVous>;
  register: UseFormRegister<ConsultationStepFormData>;
  handleSubmit: UseFormHandleSubmit<ConsultationStepFormData>;
  setValue: UseFormSetValue<ConsultationStepFormData>;
  trigger: UseFormTrigger<ConsultationStepFormData>;
  getValues: UseFormGetValues<ConsultationStepFormData>;
  watch: UseFormWatch<ConsultationStepFormData>;
}

const useConsultationStep = (): UseConsultationStepReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const toast = useToast();

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    trigger,
    getValues,
    watch,
  } = useForm<ConsultationStepFormData>({
    resolver: zodResolver(consultationStepShema),
    mode: "onChange",
  });
  const dispatch = useDispatch<AppDispatch>();

  const confirmeAppointment = async (
    data: formatDataReturn
  ): Promise<RendezVous> => {
    try {
      setIsLoading(true);
      const response = await dispatch(createAppointment(data.rdv)).unwrap();
      if (data.proche) {
        await dispatch(createProche(data.proche));
      }
      return response;
    } catch (error) {
      const formatedError = error as SupabaseError;
      switch (formatedError.code) {
        case "over_email_send_rate_limit":
          toast.error("Trop de tentatives : veuillez réessayer plus tard.");
          break;
        default:
          break;
      }
      toast.error(error.message || "Erreur lors de l'enregistrement");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    control,
    formState: { errors },
    register,
    handleSubmit,
    setValue,
    trigger,
    getValues,
    watch,
    confirmeAppointment,
  };
};

export default useConsultationStep;
