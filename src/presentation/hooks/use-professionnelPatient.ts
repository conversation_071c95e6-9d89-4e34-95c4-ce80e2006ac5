import { useCallback } from "react";
import { useAppDispatch, useAppSelector } from "./redux";
import { Patient, ProfessionnelPatient, Utilisateur } from "@/domain/models";
import {
  createProfessionalPatient,
  deleteProfessionalPatient,
  archiveProfessionalPatient,
  fetchProfessionalPatient,
  fetchProfessionalPatientById,
  fetchProfessionalPatientByPatientId,
  updateProfessionalPatient,
  clearCurrentProfessionalPatient,
} from "@/application/slices/professionnal/professionnelPatientSlice";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";
import { useToast } from "./use-toast";
import { ProfessionalRoutesNavigations } from "@/shared/constants/AppRoutesNavigation";
import { useNavigate } from "react-router-dom";

export const useProfessionnelPatient = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const toast = useToast();

  const {
    loading,
    error,
    professionalPatients,
    dataProfessionalPatients,
    selectedProfessionalPatient,
    selectedDataProfessionalPatient,
  } = useAppSelector((state) => state.professionnelPatient);

  const getProfessionnelPatient = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(fetchProfessionalPatient(id)).unwrap();
    },
    [dispatch]
  );

  const getProfessionnelPatientById = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(fetchProfessionalPatientById(id)).unwrap();
    },
    [dispatch]
  );

  const getProfessionnelPatientByPatientId = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(fetchProfessionalPatientByPatientId(id)).unwrap();
    },
    [dispatch]
  );

  const handleCreateProfessionnelPatient = useCallback(
    async (
      professionnelPatientData: Omit<ProfessionnelPatient, "id">,
      patient: Patient,
      user: Utilisateur
    ) => {
      const result = await dispatch(
        createProfessionalPatient({
          data: professionnelPatientData,
          patient: patient,
          user: user,
        })
      ).unwrap();
      return result;
    },
    [dispatch]
  );

  const handleSetPatientAsProfessionelPatient = useCallback(
    async (professionalPatientData: Omit<ProfessionnelPatient, "id">) => {
      const result = await dispatch(
        createProfessionalPatient({ data: professionalPatientData })
      ).unwrap();
      return result;
    },
    [dispatch]
  );

  const handleEndAppointment = async (
    appointment: AppointmentProfessionalDTO
  ) => {
    const { patient } = appointment;

    try {
      await getProfessionnelPatientByPatientId(patient.id);
      if (!selectedDataProfessionalPatient) {
        const data: Omit<ProfessionnelPatient, "id"> = {
          id_patient: patient.id,
          id_professionnel: appointment.id_professionnel,
          created_date: new Date(),
        };

        await dispatch(createProfessionalPatient({ data: data })).unwrap();
      }

      navigate(
        `/${
          ProfessionalRoutesNavigations.MANAGE_PATIENTS_PAGE.split("/:id")[0]
        }/${patient.id}`
      );
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : error || ErrorMessages.UNKNOWN_ERROR
      );
    }
  };

  const handleUpdateProfessionnelPatient = useCallback(
    async (
      id: number,
      professionnelPatientData: Partial<ProfessionnelPatient>
    ) => {
      const result = await dispatch(
        updateProfessionalPatient({ id, data: professionnelPatientData })
      ).unwrap();
      return result;
    },
    [dispatch]
  );

  const handleDeleteProfessionnelPatient = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      const result = await dispatch(deleteProfessionalPatient(id)).unwrap();
      return result;
    },
    [dispatch]
  );

  const handleArchiveProfessionnelPatient = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      const result = await dispatch(archiveProfessionalPatient(id)).unwrap();
      return result;
    },
    [dispatch]
  );

  const resetCurrentProfessionalPatient = useCallback(async () => {
    if (!navigator.onLine) {
      throw new Error(
        "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
      );
    }
    dispatch(clearCurrentProfessionalPatient());
  }, [dispatch]);

  return {
    loading,
    error,

    professionalPatients,
    dataProfessionalPatients,
    selectedProfessionalPatient,
    selectedDataProfessionalPatient,
    resetCurrentProfessionalPatient,
    getProfessionnelPatient,
    getProfessionnelPatientByPatientId,
    getProfessionnelPatientById,
    handleCreateProfessionnelPatient,
    handleSetPatientAsProfessionelPatient,
    handleUpdateProfessionnelPatient,
    handleDeleteProfessionnelPatient,
    handleArchiveProfessionnelPatient,
    handleEndAppointment,
  };
};
