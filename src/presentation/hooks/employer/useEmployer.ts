import { Employer } from "@/domain/models";
import { useCallback } from "react";
import {
  createEmployerThunk,
  getEmployersByProfessionalIdThunk,
  getEmployerByIdThunk,
  updateEmployerThunk,
  deleteEmployerThunk,
  setSelectedEmployer,
  clearSelectedEmployer,
} from "@/application/slices/employer/employerSlice";
import { useAppDispatch, useAppSelector } from "@/presentation/hooks/redux";
import { EmployerFormData } from "@/presentation/pages/dash/employer/EmployerSchema";
import { useToast } from "../use-toast";

export const useEmployer = () => {
  const dispatch = useAppDispatch();
  const toast = useToast();
  const {
    employers,
    selectedEmplyerSlice: selectedEmployerSlice,
    loading,
    error,
  } = useAppSelector((state) => state.employer);
  const dashId = useAppSelector((state) => state.authentification.user.id);

  const createEmploye = useCallback(
    async (data: EmployerFormData, profilePhoto: File) => {
      if (!dashId) {
        toast.error("Dash non conectee");
        return false;
      }

      const register = await dispatch(
        createEmployerThunk({
          data: {
            date_de_naissance: data.date_de_naissance,
            date_entree_en_fonction: data.date_entree_en_fonction,
            fonction: data.fonction,
            id_utilisateur: dashId,
            matricule: data.matricule,
            nom: data.nom,
            photo: data.photo,
            prenom: data.prenom,
            sexe: data.sexe,
            status_administratif: data.status_administratif,
            direction: data.direction,
            cree_a: new Date().toISOString(),
            mis_a_jour_a: new Date().toISOString(),
          },
          profilePhoto: profilePhoto,
        })
      );

      const success = register.meta.requestStatus === "fulfilled";

      if (success) {
        toast.success("Nouveau employé ajouté.");
      } else {
        const errorMessage = register.payload as string;
        toast.error(errorMessage);
        return false;
      }

      return true;
    },
    [dispatch]
  );

  const get = useCallback(
    async (id: number) => {
      await dispatch(getEmployerByIdThunk(id));
    },
    [dispatch]
  );

  const getAll = useCallback(
    async (carnetId: number) => {
      await dispatch(getEmployersByProfessionalIdThunk(carnetId));
    },
    [dispatch]
  );

  const updateEmployer = useCallback(
    async (id: number, data: EmployerFormData) => {
      const formatedData: Omit<Employer, "id"> = {
        date_de_naissance: data.date_de_naissance,
        date_entree_en_fonction: data.date_entree_en_fonction,
        fonction: data.fonction,
        id_utilisateur: dashId,
        matricule: data.matricule,
        nom: data.nom,
        photo: data.photo,
        prenom: data.prenom,
        sexe: data.sexe,
        status_administratif: data.status_administratif,
        direction: data.direction,
        cree_a: new Date().toISOString(),
        mis_a_jour_a: new Date().toISOString(),
      };

      const update = await dispatch(
        updateEmployerThunk({ id: id, data: formatedData })
      );

      const success = update.meta.requestStatus === "fulfilled";

      if (!success) {
        toast.error(
          "Une erreur a ete rencontree lors de la mise a jour de l'employer"
        );
        return false;
      } else {
        toast.success("Mise a jour de l'employer bien prise en compte.");
      }

      return true;
    },
    [dispatch]
  );

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteEmployerThunk(id));
    },
    [dispatch]
  );

  const select = useCallback(
    (employer: Employer | null) => {
      dispatch(setSelectedEmployer(employer));
    },
    [dispatch]
  );

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedEmployer());
  }, [dispatch]);

  return {
    loading,
    error,
    employers,
    selectedEmployerSlice,
    createEmploye,
    select,
    get,
    getAll,
    updateEmployer,
    remove,
    clearSelected,
  };
};
