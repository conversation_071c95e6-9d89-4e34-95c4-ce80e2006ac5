import {
  setSelectedProvince,
  setSelectedRegion,
  setSelectedDistrict,
  setSelectedCommune,
  setState,
  getProvinces,
  getRegionsByProvince,
  getDistrictsByRegion,
  getCommunesByDistrict,
  getDistricts as getDistrictsAction,
  getDistrictById as getDistrictByIdAction,
  getRegions as getRegionsAction,
  getRegionById as getRegionByIdAction,
  getCommuneById as getCommuneByIdAction,
  getProvinceById as getProvinceByIdAction,
} from "@/application/slices/locationSelector/locationSlice";
import { LocationSelectorDTO } from "@/domain/DTOS/";
import { Commune, District, Province, Region } from "@/domain/models";
import { AppDispatch, RootState } from "@/store";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

export const useLocationSelector = () => {
  const dispatch = useDispatch<AppDispatch>();

  const locationState = useSelector((state: RootState) => state.location);

  const {
    provinces,
    regions,
    districts,
    communes,
    isLoading,
    selectedProvince,
    selectedRegion,
    selectedDistrict,
    selectedCommune,
  } = locationState;

  useEffect(() => {
    if (!provinces || provinces.length === 0) {
      dispatch(getProvinces());
    }
  }, [dispatch]);

  const setLocationState = (values: Partial<LocationSelectorDTO>) => {
    dispatch(setState({ ...values }));
  };

  const getFilteredRegions = async (province_id: number) => {
    const regions = await dispatch(getRegionsByProvince(province_id));
    return regions;
  };

  const getRegions = async () => {
    const regions = await dispatch(getRegionsAction());
    return regions;
  };

  const getRegionById = async (region_id: number) => {
    const regions = await dispatch(getRegionByIdAction(region_id));
    return regions;
  };

  const getDistricts = async () => {
    await dispatch(getDistrictsAction());
  };

  const getDistrictById = async (district_id: number) => {
    const districts = await dispatch(getDistrictByIdAction(district_id));
    return districts;
  };

  const getProvinceById = async (province_id: number) => {
    await dispatch(getProvinceByIdAction(province_id));
  };

  const getCommuneById = async (commune_id: number) => {
    const communes = await dispatch(getCommuneByIdAction(commune_id));
    return communes;
  };

  const handleProvinceChange = async (value: Province) => {
    dispatch(setSelectedProvince(value));
    getFilteredRegions(value.id);
  };

  const getFilteredDistricts = async (region_id: number) => {
    const districts = await dispatch(getDistrictsByRegion(region_id));
    return districts;
  };

  const handleRegionChange = (value: Region) => {
    dispatch(setSelectedRegion(value));
    getFilteredDistricts(value.id);
  };

  const getFilteredCommunes = async (district_id: number) => {
    const communes = await dispatch(getCommunesByDistrict(district_id));
    return communes;
  };

  const handleDistrictChange = (value: District) => {
    dispatch(setSelectedDistrict(value));
    getFilteredCommunes(value.id);
  };

  const handleCommuneChange = (value: Commune) => {
    dispatch(setSelectedCommune(value));
  };

  return {
    provinces,
    regions,
    districts,
    communes,
    loading: isLoading,
    setLocationState,
    selectedProvince,
    selectedRegion,
    selectedDistrict,
    selectedCommune,
    handleProvinceChange,
    handleRegionChange,
    handleDistrictChange,
    handleCommuneChange,
    getDistricts,
    getDistrictById,
    getRegions,
    getRegionById,
    getCommuneById,
    getProvinceById,
    getFilteredRegions,
    getFilteredDistricts,
    getFilteredCommunes,
  };
};
