import { useEffect, useMemo, useState } from "react";
import { Box, TextField, Card, CardContent, Typo<PERSON>, FormControl, Select, MenuItem, InputAdornment } from "@mui/material";
import { Plus, Users, User, Filter, Search, UserCheck, UserX, Trash2 } from "lucide-react";
import AddPatientModal from "@/presentation/components/common/Modal/AddPatientModal";
import useRegister from "@/presentation/hooks/use-register";
import PatientCard from "@/presentation/components/features/patient/PatientCard/PatientCard";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { useNavigate, useLocation } from "react-router-dom";
import { ProfessionalRoutesNavigations } from "@/shared/constants/AppRoutesNavigation";
import StyledButton from "@/styles/styleMui/ConsultationSteper";
import { sexe_enum } from "@/domain/models/enums";
import { DAY_MARGIN } from "@mui/x-date-pickers/internals";
import { MatriculeGenerator } from "@/domain/services/MatriculeGenerator";

const MyPatients = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [totalPatientsMale, setTotalPatientsMale] = useState(0);
  const [totalPatientsFemale, setTotalPatientsFemale] = useState(0);
  const [genderFilter, setGenderFilter] = useState<"all" | "homme" | "femme">("all");
  const location = useLocation();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );

  // Déterminer le type de patients à afficher basé sur l'URL
  const patientType = useMemo(() => {
    if (location.pathname.includes('/actif')) return 'actif';
    if (location.pathname.includes('/decede')) return 'decede';
    if (location.pathname.includes('/supprimer')) return 'supprimer';
    return 'all';
  }, [location.pathname]);

  const { dataProfessionalPatients, getProfessionnelPatient } =
    useProfessionnelPatient();
  const { handleToggleModal } = useRegister();

  const handleAddPatientOpen = () => {
    handleToggleModal(true);
  };

  const { isOpen } = useRegister();

  // filtrer patients
  const filteredPatients = useMemo(() => {
    return (
      dataProfessionalPatients?.filter((patient) => {
        const matchesSearch =
          patient.patient.nom
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          patient.patient.prenom
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          patient.patient.unique_id
            .toLowerCase()
            .includes(searchQuery.toLowerCase());

        const matchesGender =
          genderFilter === "all" ||
          patient.patient.sexe === genderFilter;

        // Filtrer par statut (actif/décédé/supprimé)
        // Note: Pour l'instant, on simule les patients supprimés (à implémenter dans le backend)
        const patientArchive = patient.is_delete;
        const matchesStatus =
          patientType === 'all' ||
          (patientType === 'actif' && !patient.patient.decede && !patientArchive) ||
          (patientType === 'decede' && patient.patient.decede) ||
          (patientType === 'supprimer' && patient.is_delete);

        return matchesSearch && matchesGender && matchesStatus;
      }) || []
    );
  }, [dataProfessionalPatients, searchQuery, genderFilter, patientType]);

  useEffect(() => {
    if (professionalId) {
      getProfessionnelPatient(professionalId);
    }
  }, [professionalId]);

  useEffect(() => {
    if (filteredPatients) {
      const totalMale = filteredPatients.filter(
        (patient) => patient.patient.sexe === sexe_enum.homme
      ).length;
      const totalFemale = filteredPatients.filter(
        (patient) => patient.patient.sexe === sexe_enum.femme
      ).length;
      setTotalPatientsMale(totalMale);
      setTotalPatientsFemale(totalFemale);
    }
  }, [filteredPatients]);

  const navigate = useNavigate();

  // Titre et icône dynamiques
  const pageConfig = useMemo(() => {
    switch (patientType) {
      case 'actif':
        return {
          title: 'Patients Actifs',
          subtitle: 'Liste des patients en cours de traitement',
          icon: <UserCheck size={18} className="text-emerald-500 dark:text-emerald-400" />,
          color: 'text-emerald-600 dark:text-emerald-400',
          bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',
          borderColor: 'border-emerald-200 dark:border-emerald-800'
        };
      case 'decede':
        return {
          title: 'Patients Décédés',
          subtitle: 'Liste des patients décédés',
          icon: <UserX size={24} className="text-rose-500 dark:text-rose-400" />,
          color: 'text-rose-600 dark:text-rose-400',
          bgColor: 'bg-rose-50 dark:bg-rose-900/20',
          borderColor: 'border-rose-200 dark:border-rose-800'
        };
      case 'supprimer':
        return {
          title: 'Patients Supprimés',
          subtitle: 'Liste des patients retirés de votre suivi',
          icon: <Trash2 size={18} className="text-amber-500 dark:text-amber-400" />,
          color: 'text-amber-600 dark:text-amber-400',
          bgColor: 'bg-amber-50 dark:bg-amber-900/20',
          borderColor: 'border-amber-200 dark:border-amber-800'
        };
      default:
        return {
          title: 'Tous les Patients',
          subtitle: 'Liste complète de vos patients',
          icon: <Users size={18} className="text-blue-500" />,
          color: 'text-blue-600 dark:text-blue-400'
        };
    }
  }, [patientType]);

  return (
    <Box className="w-full ">
      
     

      {/* Barre de recherche et contrôles modernes */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 sm:p-6 mb-6 transition-colors duration-200">
        {/* Version mobile - layout vertical */}
        <div className="block sm:hidden space-y-4">
          {/* Barre de recherche mobile */}
          <div className="relative">
            <TextField
              fullWidth
              placeholder="Rechercher un patient..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search size={18} className="text-gray-400 dark:text-gray-500" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '12px',
                  backgroundColor: '#f8fafc',
                  border: '1px solid #e2e8f0',
                  transition: 'all 0.2s ease',
                  color: '#1f2937',
                  '& fieldset': {
                    border: 'none',
                  },
                  '&:hover': {
                    backgroundColor: '#f1f5f9',
                    borderColor: '#cbd5e1',
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#ffffff',
                    borderColor: '#27aae1',
                    boxShadow: '0 0 0 3px rgba(39, 170, 225, 0.1)',
                  },
                },
                '& .MuiInputBase-input': {
                  '&::placeholder': {
                    color: '#9ca3af',
                    opacity: 1,
                  },
                },
              }}
              className="[&_.MuiOutlinedInput-root]:dark:!bg-gray-700 [&_.MuiOutlinedInput-root]:dark:!border-gray-600 [&_.MuiOutlinedInput-root]:dark:!text-gray-100 [&_.MuiOutlinedInput-root:hover]:dark:!bg-gray-600 [&_.MuiOutlinedInput-root.Mui-focused]:dark:!bg-gray-700 [&_.MuiInputBase-input::placeholder]:dark:!text-gray-400"
            />
          </div>

      
    
          {/* Contrôles mobile */}
          <div className="flex gap-3">
            <FormControl size="small" sx={{ flex: 1 }}>
              <Select
                value={genderFilter}
                onChange={(e) => setGenderFilter(e.target.value as "all" | "homme" | "femme")}
                displayEmpty
                sx={{
                  borderRadius: '12px',
                  backgroundColor: '#f8fafc',
                  border: '1px solid #e2e8f0',
                  color: '#1f2937',
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                  '&:hover': {
                    backgroundColor: '#f1f5f9',
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#ffffff',
                    borderColor: '#27aae1',
                    boxShadow: '0 0 0 3px rgba(39, 170, 225, 0.1)',
                  },
                }}
                className="dark:!bg-gray-700 dark:!border-gray-600 dark:!text-gray-100 dark:hover:!bg-gray-600 dark:focus-within:!bg-gray-700"
              >
                <MenuItem value="all">
                  <div className="flex items-center gap-2">
                    <Filter size={16} className="text-gray-500 dark:text-gray-400" />
                    <span className="text-gray-700 dark:text-gray-200">Tous</span>
                  </div>
                </MenuItem>
                <MenuItem value="homme">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700 dark:text-gray-200">Hommes</span>
                  </div>
                </MenuItem>
                <MenuItem value="femme">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                    <span className="text-gray-700 dark:text-gray-200">Femmes</span>
                  </div>
                </MenuItem>
              </Select>
            </FormControl>

            <button
              onClick={handleAddPatientOpen}
              className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-4 py-2 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center gap-2 whitespace-nowrap"
            >
              <Plus size={18} />
              <span>Ajouter</span>
            </button>
          </div>
        </div>

        {/* Version desktop - layout horizontal */}
        <div className="hidden sm:flex items-center gap-4">
          {/* Barre de recherche desktop */}
          <div className="flex-1 max-w-md">
            <TextField
              fullWidth
              placeholder="Rechercher un patient par nom, prénom ou ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              size="medium"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search size={20} className="text-gray-400 dark:text-gray-500" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '16px',
                  backgroundColor: '#f8fafc',
                  border: '1px solid #e2e8f0',
                  transition: 'all 0.3s ease',
                  fontSize: '0.95rem',
                  color: '#1f2937',
                  '& fieldset': {
                    border: 'none',
                  },
                  '&:hover': {
                    backgroundColor: '#f1f5f9',
                    borderColor: '#cbd5e1',
                    transform: 'translateY(-1px)',
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#ffffff',
                    borderColor: '#27aae1',
                    boxShadow: '0 0 0 4px rgba(39, 170, 225, 0.1)',
                    transform: 'translateY(-1px)',
                  },
                },
                '& .MuiInputBase-input': {
                  '&::placeholder': {
                    color: '#9ca3af',
                    opacity: 1,
                  },
                },
              }}
              className="[&_.MuiOutlinedInput-root]:dark:!bg-gray-700 [&_.MuiOutlinedInput-root]:dark:!border-gray-600 [&_.MuiOutlinedInput-root]:dark:!text-gray-100 [&_.MuiOutlinedInput-root:hover]:dark:!bg-gray-600 [&_.MuiOutlinedInput-root.Mui-focused]:dark:!bg-gray-700 [&_.MuiInputBase-input::placeholder]:dark:!text-gray-400"
            />
          </div>

          {/* Filtre desktop */}
          <FormControl size="medium" sx={{ minWidth: 140 }}>
            <Select
              value={genderFilter}
              onChange={(e) => setGenderFilter(e.target.value as "all" | "homme" | "femme")}
              displayEmpty
              sx={{
                borderRadius: '16px',
                backgroundColor: '#f8fafc',
                border: '1px solid #e2e8f0',
                transition: 'all 0.3s ease',
                color: '#1f2937',
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
                '&:hover': {
                  backgroundColor: '#f1f5f9',
                  borderColor: '#cbd5e1',
                  transform: 'translateY(-1px)',
                },
                '&.Mui-focused': {
                  backgroundColor: '#ffffff',
                  borderColor: '#27aae1',
                  boxShadow: '0 0 0 4px rgba(39, 170, 225, 0.1)',
                },
              }}
              className="dark:!bg-gray-700 dark:!border-gray-600 dark:!text-gray-100 dark:hover:!bg-gray-600 dark:focus-within:!bg-gray-700"
            >
              <MenuItem value="all">
                <div className="flex items-center gap-2">
                  <Filter size={16} className="text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-700 dark:text-gray-200">Tous les patients</span>
                </div>
              </MenuItem>
              <MenuItem value="homme">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-200">Patients hommes</span>
                </div>
              </MenuItem>
              <MenuItem value="femme">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-200">Patientes femmes</span>
                </div>
              </MenuItem>
            </Select>
          </FormControl>

          {/* Bouton d'ajout desktop */}
          <button
            onClick={handleAddPatientOpen}
            className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-6 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3 whitespace-nowrap group"
          >
            <Plus size={20} className="group-hover:rotate-90 transition-transform duration-300" />
            <span className="hidden md:inline">Ajouter un patient</span>
            <span className="md:hidden">Ajouter</span>
          </button>
        </div>
      </div>

      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        {/* Total Patients */}
        <Card sx={{
          borderRadius: '8px',
          boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
          backgroundColor: 'white',
        }}
        className="dark:!bg-gray-800 dark:!shadow-lg"
        >
          <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
            <div className="flex items-center justify-between">
              <div>
                <Typography variant="caption" sx={{
                  fontSize: '0.7rem',
                  lineHeight: 1,
                  color: '#6b7280',
                }}
                className="dark:!text-gray-400"
                >
                  {patientType === 'actif' ? 'PATIENTS ACTIFS' :
                   patientType === 'decede' ? 'PATIENTS DÉCÉDÉS' :
                   patientType === 'supprimer' ? 'PATIENTS SUPPRIMÉS' :
                   'TOTAL PATIENTS'}
                </Typography>
                <Typography variant="h5" sx={{
                  fontWeight: 'bold',
                  color: '#2c3e50',
                  mt: 0.25,
                }}
                className="dark:!text-gray-100"
                >
                  {filteredPatients.length}
                </Typography>
              </div>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: '8px',
                  backgroundColor: '#4285f4',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 8px rgba(66, 133, 244, 0.3)',
                }}
              >
                <Users size={16} color="white" />
              </Box>
            </div>
          </CardContent>
        </Card>

        {/* Hommes */}
        <Card sx={{
          borderRadius: '8px',
          boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
          backgroundColor: 'white',
        }}
        className="dark:!bg-gray-800 dark:!shadow-lg"
        >
          <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
            <div className="flex items-center justify-between">
              <div>
                <Typography variant="caption" sx={{
                  fontSize: '0.7rem',
                  lineHeight: 1,
                  color: '#6b7280',
                }}
                className="dark:!text-gray-400"
                >
                  HOMMES
                </Typography>
                <Typography variant="h5" sx={{
                  fontWeight: 'bold',
                  color: '#2c3e50',
                  mt: 0.25,
                }}
                className="dark:!text-gray-100"
                >
                  {totalPatientsMale}
                </Typography>
              </div>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: '8px',
                  backgroundColor: '#6c5ce7',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 8px rgba(108, 92, 231, 0.3)',
                }}
              >
                <User size={16} color="white" />
              </Box>
            </div>
          </CardContent>
        </Card>

        {/* Femmes */}
        <Card sx={{
          borderRadius: '8px',
          boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
          backgroundColor: 'white',
        }}
        className="dark:!bg-gray-800 dark:!shadow-lg"
        >
          <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
            <div className="flex items-center justify-between">
              <div>
                <Typography variant="caption" sx={{
                  fontSize: '0.7rem',
                  lineHeight: 1,
                  color: '#6b7280',
                }}
                className="dark:!text-gray-400"
                >
                  FEMMES
                </Typography>
                <Typography variant="h5" sx={{
                  fontWeight: 'bold',
                  color: '#2c3e50',
                  mt: 0.25,
                }}
                className="dark:!text-gray-100"
                >
                  {totalPatientsFemale}
                </Typography>
              </div>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: '8px',
                  backgroundColor: '#fd79a8',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 8px rgba(253, 121, 168, 0.3)',
                }}
              >
                <User size={16} color="white" />
              </Box>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des patients */}
      <div className="mb-4">
          <div className="mx-2 mb-10">
            <div className="flex items-center gap-3 mb-2 ">
              {pageConfig.icon}
              <Typography
                variant="h1"
                className={`font-bold ${pageConfig.color}  `}
                sx={{ fontSize: '1.2rem'   } }
              >
                {pageConfig.title} ({filteredPatients.length})
              </Typography>
            </div>
            <Typography
              variant="body1"
              className="text-gray-600 dark:text-gray-400"
              sx={{ fontSize: '0.95rem' }}
            >
              {pageConfig.subtitle}
            </Typography>

      
          </div>

        {filteredPatients.length === 0 ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={8}
            sx={{
              backgroundColor: 'rgba(0, 0, 0, 0.02)',
              borderRadius: 2,
              border: '1px dashed rgba(0, 0, 0, 0.12)',
            }}
            className="dark:!bg-white/5 dark:!border-white/20"
          >
            <Typography variant="h6" sx={{
              color: '#6b7280',
            }}
            className="dark:!text-gray-400"
            gutterBottom>
              Aucun patient trouvé
            </Typography>
            <Typography variant="body2" sx={{
              color: '#6b7280',
            }}
            className="dark:!text-gray-400"
            textAlign="center">
              {searchQuery
                ? "Aucun patient ne correspond à votre recherche"
                : "Vous n'avez aucun patient pour le moment"
              }
            </Typography>
          </Box>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredPatients.map((patient) => (
              <div
                style={{ cursor: "pointer" }}
                key={patient.id}
                onClick={() =>
                  navigate(
                    `/${ProfessionalRoutesNavigations.MANAGE_PATIENTS_PAGE.split("/:id")[0]}/${patient.patient.id}`
                  )
                }
              >
                <PatientCard patient={patient} />
              </div>
            ))}
          </div>
        )}
      </div>
      {isOpen && <AddPatientModal />}
    </Box>
  );
};

export default MyPatients;
