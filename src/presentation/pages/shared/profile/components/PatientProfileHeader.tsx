import { <PERSON>, CardContent, But<PERSON> } from "@mui/material";
import { Activity, Droplets, Edit, Heart, User, Users } from "lucide-react";
import { Patient } from "@/domain/models";

interface PatientProfileHeaderProps {
    patient: Patient;
    calculateAge: (birthDate: Date) => number;
    onEdit?: () => void;
}

const PatientProfileHeader = ({ patient, calculateAge, onEdit }: PatientProfileHeaderProps) => {
    return (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Carte de profil */}
            <Card className="lg:col-span-2 relative overflow-hidden border-0 bg-white shadow-2xl">
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/10 via-blue-500/10 to-purple-600/10"></div>
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-blue-400/20 to-transparent rounded-full blur-3xl"></div>

                <CardContent className="relative p-8">
                    <div className="flex flex-col md:flex-row items-start gap-8">
                        {/* Section Avatar Moderne */}
                        <div className="relative flex-shrink-0">
                            <div className="w-32 h-32 bg-gradient-to-br from-emerald-400 to-blue-600 rounded-3xl p-1 shadow-2xl">
                                <div className="w-full h-full bg-white rounded-3xl flex items-center justify-center">
                                    <User className="h-16 w-16 text-gray-600" />
                                </div>
                            </div>

                            {/* Indicateurs de statut */}
                            <div className="absolute -top-2 -right-2 flex flex-col gap-2">
                                <div className="w-8 h-8 bg-green-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                                    <Activity className="h-4 w-4 text-white" />
                                </div>
                                {patient?.donneur_sang && (
                                    <div className="w-8 h-8 bg-red-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                                        <Heart className="h-4 w-4 text-white" />
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Détails du patient */}
                        <div className="flex-1 space-y-6">
                            <div>
                                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-1">
                                    {patient?.nom}
                                </h1>
                                <h2 className="text-xl md:text-2xl font-semibold text-gray-700 mb-3">
                                    {patient?.prenom}
                                </h2>

                                <div className="flex flex-wrap gap-3">
                                    <div className="bg-emerald-100 text-emerald-800 border border-emerald-200 px-4 py-2 text-sm font-semibold rounded-full">
                                        ID: {patient?.unique_id}
                                    </div>
                                    {patient?.date_naissance && (
                                        <div className="bg-blue-100 text-blue-800 border border-blue-200 px-4 py-2 text-sm font-semibold rounded-full">
                                            {calculateAge(patient.date_naissance)} Ans
                                        </div>
                                    )}
                                    {patient?.sexe && (
                                        <div className="bg-purple-100 text-purple-800 border border-purple-200 px-4 py-2 text-sm font-semibold rounded-full">
                                            {patient.sexe}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Statistiques rapides */}
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                {patient?.groupe_sanguin && (
                                    <div className="text-center p-4 bg-red-50 rounded-2xl border border-red-100">
                                        <Droplets className="h-6 w-6 text-red-500 mx-auto mb-2" />
                                        <p className="text-2xl font-bold text-red-600">{patient?.groupe_sanguin}</p>
                                        <p className="text-xs text-red-500 font-medium">Groupe sanguin</p>
                                    </div>
                                )}
                                <div className="text-center p-4 bg-pink-50 rounded-2xl border border-pink-100">
                                    <Heart className="h-6 w-6 text-pink-500 mx-auto mb-2" />
                                    <p className="text-2xl font-bold text-pink-600">{patient?.donneur_sang ? 'Oui' : 'Non'}</p>
                                    <p className="text-xs text-pink-500 font-medium">Donneur</p>
                                </div>
                                {patient?.nb_enfant !== undefined && (
                                    <div className="text-center p-4 bg-blue-50 rounded-2xl border border-blue-100">
                                        <Users className="h-6 w-6 text-blue-500 mx-auto mb-2" />
                                        <p className="text-2xl font-bold text-blue-600">{patient.nb_enfant}</p>
                                        <p className="text-xs text-blue-500 font-medium">Enfants</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Panel d'actions */}
            <Card
                className="border-0 text-white shadow-2xl"
                style={{
                    background: 'linear-gradient(to bottom right, #27aae1, #027f3b)'
                }}
            >
                <CardContent className="p-8 text-center space-y-6">
                    <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto">
                        <Edit className="h-8 w-8 text-white" />
                    </div>

                    <div>
                        <h3 className="text-2xl font-bold mb-2">Gestion du Profil</h3>
                        <p className="text-indigo-100 text-sm">Modifiez les informations du patient</p>
                    </div>

                    <Button
                        onClick={onEdit}
                        sx={{
                            width: '100%',
                            backgroundColor: 'white',
                            color: '#9333ea',
                            fontWeight: '600',
                            padding: '12px 24px',
                            borderRadius: '12px',
                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                            textTransform: 'none',
                            fontSize: '16px',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                                backgroundColor: '#faf5ff',
                                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                                transform: 'translateY(-1px)'
                            }
                        }}
                    >
                        Modifier le profil
                    </Button>

                    <div className="pt-4 border-t border-white/20">
                        <p className="text-xs text-indigo-200">Dernière modification</p>
                        <p className="text-sm font-medium text-white">Aujourd'hui</p>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default PatientProfileHeader;
