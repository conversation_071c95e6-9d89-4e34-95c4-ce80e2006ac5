import { <PERSON>, CardContent } from "@mui/material";
import { Mail, MapPin, Phone, User } from "lucide-react";
import { Patient } from "@/domain/models";

interface PatientInformationGridProps {
    patient: Patient;
    communeName: string;
    districtName: string;
    regionName: string;
}

const PatientInformationGrid = ({ 
    patient, 
    communeName, 
    districtName, 
    regionName 
}: PatientInformationGridProps) => {
    return (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
            {/* Carte de contact */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="pb-3 p-6">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                            <Mail className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">
                            Contact
                        </h3>
                    </div>
                </div>

                <CardContent className="space-y-4">
                    {patient?.email && (
                        <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-xl border border-blue-100">
                            <Mail className="h-5 w-5 text-blue-600" />
                            <div>
                                <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Email</p>
                                <p className="text-gray-800 font-medium">{patient.email}</p>
                            </div>
                        </div>
                    )}

                    {patient?.telephone && (
                        <div className="flex items-center gap-4 p-4 bg-green-50 rounded-xl border border-green-100">
                            <Phone className="h-5 w-5 text-green-600" />
                            <div>
                                <p className="text-xs font-medium text-green-600 uppercase tracking-wide">Téléphone</p>
                                <p className="text-gray-800 font-medium">{patient.telephone}</p>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Carte de localisation */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="pb-3 p-6">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                            <MapPin className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">
                            Localisation
                        </h3>
                    </div>
                </div>

                <CardContent className="space-y-4">
                    {patient?.adresse && (
                        <div className="p-4 bg-orange-50 rounded-xl border border-orange-100">
                            <p className="text-xs font-medium text-orange-600 uppercase tracking-wide mb-2">Adresse</p>
                            <p className="text-gray-800 font-medium">{patient.adresse}</p>
                        </div>
                    )}

                    <div className="grid grid-cols-1 gap-3">
                        <div className="p-3 bg-gray-50 rounded-lg">
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Commune</p>
                            <p className="text-gray-800 font-medium">{communeName}</p>
                        </div>
                        <div className="p-3 bg-gray-50 rounded-lg">
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">District</p>
                            <p className="text-gray-800 font-medium">{districtName}</p>
                        </div>
                        <div className="p-3 bg-gray-50 rounded-lg">
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Région</p>
                            <p className="text-gray-800 font-medium">{regionName}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Carte d'informations personnelles */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="pb-3 p-6">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                            <User className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">
                            Information
                        </h3>
                    </div>
                </div>

                <CardContent className="space-y-4">
                    {patient?.date_naissance && (
                        <div className="p-4 bg-purple-50 rounded-xl border border-purple-100">
                            <p className="text-xs font-medium text-purple-600 uppercase tracking-wide mb-2">Date de naissance</p>
                            <p className="text-gray-800 font-medium">
                                {new Date(patient.date_naissance).toLocaleDateString('fr-FR', { 
                                    day: 'numeric', 
                                    month: 'long', 
                                    year: 'numeric' 
                                })}
                            </p>
                        </div>
                    )}

                    {patient?.situation_matrimonial && (
                        <div className="p-4 bg-pink-50 rounded-xl border border-pink-100">
                            <p className="text-xs font-medium text-pink-600 uppercase tracking-wide mb-2">État civil</p>
                            <p className="text-gray-800 font-medium">{patient.situation_matrimonial}</p>
                        </div>
                    )}

                    {patient?.nationalite && (
                        <div className="p-4 bg-indigo-50 rounded-xl border border-indigo-100">
                            <p className="text-xs font-medium text-indigo-600 uppercase tracking-wide mb-2">Nationalité</p>
                            <p className="text-gray-800 font-medium">{patient.nationalite}</p>
                        </div>
                    )}

                    {patient?.profession && (
                        <div className="p-4 bg-indigo-50 rounded-xl border border-indigo-100">
                            <p className="text-xs font-medium text-indigo-600 uppercase tracking-wide mb-2">Profession</p>
                            <p className="text-gray-800 font-medium">{patient.profession}</p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};

export default PatientInformationGrid;
