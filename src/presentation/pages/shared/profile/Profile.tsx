import { utilisateurs_role_enum } from "@/domain/models/enums";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import useGetAuthenticatedUser from "@/presentation/hooks/user/use-get-authenticated-user";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import ProfessionalProfile from "./ProfessionalProfile.tsx";
import PatientProfile from "./PatientProfile.tsx";

const Profile = () => {
  const { user, loading, error } = useGetAuthenticatedUser();
  const params = useParams<{ id?: string }>();
  const navigate = useNavigate();

  useEffect(() => {
    const verifyIdIntegrity = () => {
      if (!loading && user && user.id && params.id) {
        if (parseInt(params.id) !== user.id) {
          navigate(PublicRoutesNavigation.MAIN_PAGE);
        }
      }
    };
    verifyIdIntegrity();
  }, [loading, user, params, navigate]);

  if ((error && !loading) || (!loading && (!user || !user.role))) {
    history.back();
    return null;
  }

  if (user && !loading) {
    switch (user.role) {
      case utilisateurs_role_enum.PATIENT:
        return <PatientProfile />;

      case utilisateurs_role_enum.PROFESSIONNEL:
        return <ProfessionalProfile />;

      case utilisateurs_role_enum.ADMIN:
        return <div>Admin profile</div>;

      default:
        return null;
    }
  }

  return null;
};

export default Profile;
