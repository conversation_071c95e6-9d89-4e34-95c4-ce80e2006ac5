import { useEffect, useState } from "react";
import { usePatient } from "@/presentation/hooks/patient/use-patient";
import { useAppSelector } from "@/presentation/hooks/redux";
import { Card, CardContent } from "@mui/material";
import { Activity, User } from "lucide-react";
import { useLocationSelector } from "@/presentation/hooks/use-location-selector";
import { useRestoreAuth } from "@/presentation/hooks/use-restore-auth";
import { PatientProfileHeader, PatientInformationGrid } from "./components";


const PatientProfile = () => {
    const { isRestoreDone } = useRestoreAuth();

    const patientId = useAppSelector(
        (state) => state.authentification.userData?.id
    );
    const authLoading = useAppSelector(
        (state) => state.authentification.loading
    );
    const { loading, patient, getPatientById } = usePatient();

    const { getCommuneById, getRegionById, getDistrictById, selectedDistrict, selectedCommune, selectedRegion } = useLocationSelector();
    const [communeName, setCommuneName] = useState<string>("");
    const [regionName, setRegionName] = useState<string>("");
    const [districtName, setDistrictName] = useState<string>("");


    const calculateAge = (birthDate: Date) => {
        const today = new Date();
        const birth = new Date(patient?.date_naissance);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        return age;
    };

    useEffect(() => {
        if (patientId) {
            getPatientById(patientId);
        }
    }, [patientId]);

    // Récupérer les données de localisation quand les données du patient sont disponibles
    useEffect(() => {
        if (patient?.commune) {
            const fetchCommuneName = async () => {
                try {
                    await getCommuneById(parseInt(patient.commune));
                } catch (error) {
                    console.error("Erreur lors de la récupération de la commune:", error);
                    setCommuneName("Commune non disponible");
                }
            };
            fetchCommuneName();
        }
    }, [patient?.commune, getCommuneById]);

    useEffect(() => {
        if (patient?.district) {
            const fetchDistrictName = async () => {
                try {
                    await getDistrictById(parseInt(patient.district));
                } catch (error) {
                    console.error("Erreur lors de la récupération du district:", error);
                    setDistrictName("District non disponible");
                }
            };
            fetchDistrictName();
        }
    }, [patient?.district, getDistrictById]);

    useEffect(() => {
        if (patient?.region) {
            const fetchRegionName = async () => {
                try {
                    await getRegionById(parseInt(patient.region));
                } catch (error) {
                    console.error("Erreur lors de la récupération de la région:", error);
                    setRegionName("Région non disponible");
                }
            };
            fetchRegionName();
        }
    }, [patient?.region, getRegionById]);

    // Mettre à jour les noms quand les données sont récupérées
    useEffect(() => {
        if (selectedCommune) {
            setCommuneName(selectedCommune.nom);
        }
    }, [selectedCommune]);

    useEffect(() => {
        if (selectedDistrict) {
            setDistrictName(selectedDistrict.libelle);
        }
    }, [selectedDistrict]);

    useEffect(() => {
        if (selectedRegion) {
            setRegionName(selectedRegion.nom);
        }
    }, [selectedRegion]);

    // Gestion de l'état de chargement - attendre la restauration des données d'auth
    if (!isRestoreDone || authLoading || loading) {
        return (
            <div className="max-w-7xl mx-auto space-y-8 p-8">
                <div className="flex items-center justify-center min-h-[400px]">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p className="text-gray-600">Chargement du profil...</p>
                    </div>
                </div>
            </div>
        );
    }

    // Vérification si l'utilisateur est authentifié après la restauration
    if (isRestoreDone && !patientId) {
        return (
            <div className="max-w-7xl mx-auto space-y-8 p-8">
                <div className="flex items-center justify-center min-h-[400px]">
                    <div className="text-center">
                        <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h2 className="text-xl font-semibold text-gray-700 mb-2">Non authentifié</h2>
                        <p className="text-gray-600">Veuillez vous connecter pour accéder à votre profil.</p>
                    </div>
                </div>
            </div>
        );
    }

    // Vérification si le patient existe
    if (!patient && !loading) {
        return (
            <div className="max-w-7xl mx-auto space-y-8 p-8">
                <div className="flex items-center justify-center min-h-[400px]">
                    <div className="text-center">
                        <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h2 className="text-xl font-semibold text-gray-700 mb-2">Profil non trouvé</h2>
                        <p className="text-gray-600">Impossible de charger les informations du patient.</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="max-w-7xl mx-auto space-y-8">

            {/* En-tête du profil patient */}
            <PatientProfileHeader
                patient={patient!}
                calculateAge={calculateAge}
                onEdit={() => {
                    // Logique de modification du profil
                    console.log("Modifier le profil");
                }}
            />

            {/* Grille d'informations du patient */}
            <PatientInformationGrid
                patient={patient!}
                communeName={communeName}
                districtName={districtName}
                regionName={regionName}
            />

            {/* Medical Summary Banner */}
            <Card className="border-0 bg-gradient-to-r from-emerald-500 via-teal-600 to-cyan-600 text-white shadow-2xl"
                style={{
                    background: 'linear-gradient(to bottom right, #27aae1, #027f3b)'
                }}>
                <CardContent className="p-8">
                    <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                        <div className="flex items-center gap-6">
                            <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center">
                                <Activity className="h-8 w-8 text-white" />
                            </div>
                            <div>
                                <h3 className="text-2xl font-bold mb-1">Résumé Médical</h3>
                                <p className="text-emerald-100">Informations de santé importantes</p>
                            </div>
                        </div>

                        <div className="flex flex-wrap gap-4">
                            {patient?.groupe_sanguin && (
                                <div className="bg-white/20 backdrop-blur-sm rounded-xl px-4 py-3 text-center">
                                    <p className="text-2xl font-bold">{patient.groupe_sanguin}</p>
                                    <p className="text-xs text-emerald-100">Groupe sanguin</p>
                                </div>
                            )}
                            <div className="bg-white/20 backdrop-blur-sm rounded-xl px-4 py-3 text-center">
                                <p className="text-2xl font-bold">{patient?.donneur_sang ? '✓' : '✗'}</p>
                                <p className="text-xs text-emerald-100">Donneur</p>
                            </div>

                            <div className="bg-white/20 backdrop-blur-sm rounded-xl px-4 py-3 text-center">
                                <p className="text-2xl font-bold">{calculateAge(patient.date_naissance)}</p>
                                <p className="text-xs text-emerald-100">Âge</p>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>

    );
};

export default PatientProfile;
