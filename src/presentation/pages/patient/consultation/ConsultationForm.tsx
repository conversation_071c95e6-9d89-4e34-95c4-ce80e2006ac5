import { motion } from "framer-motion";
import {
  Control,
  FieldErrors,
  UseFormGetValues,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";
import ConsultationStep1 from "@/presentation/components/features/patient/consultationStepper/ConsultationStep1/ConsultationStep1";
import ConsultationStep2 from "@/presentation/components/features/patient/consultationStepper/ConsultationStep2/ConsultationStep2";
import { CONSULTATION_STEPS } from "@/shared/constants/ConsultationtSteps";
import ConsultationStep4 from "@/presentation/components/features/patient/consultationStepper/ConsultationStep4/ConsultationStep4";
import ConsultationStep3 from "@/presentation/components/features/patient/consultationStepper/ConsultationStep3/ConsultationStep3";
import { NavigationButtons } from "./NavigationButtons";
import { RendezVous } from "@/domain/models";
import ConsultationStep5 from "@/presentation/components/features/patient/consultationStepper/ConsultationStep5/ConsultationStep5";

export interface ConsultationFormProps {
  activeStep: number;
  control: Control<ConsultationStepFormData>;
  errors: FieldErrors<ConsultationStepFormData>;
  isDisabled: boolean;
  isLoading: boolean;
  onSubmit: () => Promise<RendezVous>;
  register: UseFormRegister<ConsultationStepFormData>;
  setValue: UseFormSetValue<ConsultationStepFormData>;
  watch: UseFormWatch<ConsultationStepFormData>;
  onBack: (count: number) => void;
  onNext: (count: number) => void;
}

const ConsultationForm = ({
  activeStep,
  control,
  errors,
  isDisabled,
  isLoading,
  onSubmit,
  register,
  setValue,
  watch,
  onBack,
  onNext,
}: ConsultationFormProps): JSX.Element => {
  const renderFormField = (activeStep: number) => {
    switch (activeStep) {
      case 0:
        return (
          <ConsultationStep1
            control={control}
            errors={errors}
            onSubmit={onSubmit}
            register={register}
            setValue={setValue}
          />
        );
      case 1:
        return (
          <ConsultationStep2
            control={control}
            errors={errors}
            onSubmit={onSubmit}
            register={register}
            setValue={setValue}
            onNext={onNext}
          />
        );
      case 2:
        return (
          <ConsultationStep4
            control={control}
            errors={errors}
            register={register}
            setValue={setValue}
            onNext={onNext}
            isLoading={isLoading}
            onSubmit={onSubmit}
            watch={watch}
          />
        );
      case 3:
        return <ConsultationStep5 />;

      default:
        break;
    }
  };
  return (
    <div className="lg:w-3/5 p-4 lg:px-8 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <div className="flex-1">{renderFormField(activeStep)}</div>

        {activeStep != 4 && (
          <div className="mt-2 pt-2 border-t border-gray-200">
            <NavigationButtons
              activeStep={activeStep}
              onBack={onBack}
              onNext={onNext}
              isDisabled={isDisabled}
            />
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default ConsultationForm;
