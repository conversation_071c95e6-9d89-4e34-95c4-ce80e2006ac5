import {
  TextField,
  FormControl,
  Select,
  MenuItem,
  InputAdornment,
} from "@mui/material";
import {
  Plus,
  Filter,
  Search,
  Building2,
} from "lucide-react";

/**
 * Props pour le composant EmployeesSearchControls
 */
interface EmployeesSearchControlsProps {
  /** Valeur de la recherche */
  searchQuery: string;
  /** Fonction pour mettre à jour la recherche */
  setSearchQuery: (query: string) => void;
  /** Filtre de genre sélectionné */
  genderFilter: "all" | "homme" | "femme";
  /** Fonction pour mettre à jour le filtre de genre */
  setGenderFilter: (filter: "all" | "homme" | "femme") => void;
  /** Filtre de direction sélectionné */
  directionFilter: string;
  /** Fonction pour mettre à jour le filtre de direction */
  setDirectionFilter: (filter: string) => void;
  /** Liste des directions disponibles */
  availableDirections: string[];
  /** Fonction appelée lors du clic sur le bouton d'ajout */
  onAddEmployee: () => void;
}

/**
 * Composant qui affiche les contrôles de recherche et filtres pour desktop
 * 
 * @param props - Les propriétés du composant
 * @returns JSX.Element
 * 
 * @example
 * ```tsx
 * <EmployeesSearchControls
 *   searchQuery={searchQuery}
 *   setSearchQuery={setSearchQuery}
 *   genderFilter={genderFilter}
 *   setGenderFilter={setGenderFilter}
 *   directionFilter={directionFilter}
 *   setDirectionFilter={setDirectionFilter}
 *   availableDirections={availableDirections}
 *   onAddEmployee={handleAddEmployedOpen}
 * />
 * ```
 */
const EmployeesSearchControls = ({
  searchQuery,
  setSearchQuery,
  genderFilter,
  setGenderFilter,
  directionFilter,
  setDirectionFilter,
  availableDirections,
  onAddEmployee,
}: EmployeesSearchControlsProps) => {
  return (
    <div className="hidden sm:flex items-center gap-4">
      {/* Barre de recherche desktop */}
      <div className="flex-1 max-w-md">
        <TextField
          fullWidth
          placeholder="Rechercher un employé par nom, prénom ou matricule..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          size="medium"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search
                  size={20}
                  className="text-gray-400 dark:text-gray-500"
                />
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              borderRadius: "16px",
              backgroundColor: "#f8fafc",
              border: "1px solid #e2e8f0",
              transition: "all 0.3s ease",
              fontSize: "0.95rem",
              color: "#1f2937",
              "& fieldset": {
                border: "none",
              },
              "&:hover": {
                backgroundColor: "#f1f5f9",
                borderColor: "#cbd5e1",
                transform: "translateY(-1px)",
              },
              "&.Mui-focused": {
                backgroundColor: "#ffffff",
                borderColor: "#27aae1",
                boxShadow: "0 0 0 4px rgba(39, 170, 225, 0.1)",
                transform: "translateY(-1px)",
              },
            },
            "& .MuiInputBase-input": {
              "&::placeholder": {
                color: "#9ca3af",
                opacity: 1,
              },
            },
          }}
          className="[&_.MuiOutlinedInput-root]:dark:!bg-gray-700 [&_.MuiOutlinedInput-root]:dark:!border-gray-600 [&_.MuiOutlinedInput-root]:dark:!text-gray-100 [&_.MuiOutlinedInput-root:hover]:dark:!bg-gray-600 [&_.MuiOutlinedInput-root.Mui-focused]:dark:!bg-gray-700 [&_.MuiInputBase-input::placeholder]:dark:!text-gray-400"
        />
      </div>

      {/* Filtre par genre desktop */}
      <FormControl size="medium" sx={{ minWidth: 140 }}>
        <Select
          value={genderFilter}
          onChange={(e) =>
            setGenderFilter(e.target.value as "all" | "homme" | "femme")
          }
          displayEmpty
          sx={{
            borderRadius: "16px",
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            transition: "all 0.3s ease",
            color: "#1f2937",
            "& .MuiOutlinedInput-notchedOutline": {
              border: "none",
            },
            "&:hover": {
              backgroundColor: "#f1f5f9",
              borderColor: "#cbd5e1",
              transform: "translateY(-1px)",
            },
            "&.Mui-focused": {
              backgroundColor: "#ffffff",
              borderColor: "#27aae1",
              boxShadow: "0 0 0 4px rgba(39, 170, 225, 0.1)",
            },
          }}
          className="dark:!bg-gray-700 dark:!border-gray-600 dark:!text-gray-100 dark:hover:!bg-gray-600 dark:focus-within:!bg-gray-700"
        >
          <MenuItem value="all">
            <div className="flex items-center gap-2">
              <Filter
                size={16}
                className="text-gray-500 dark:text-gray-400"
              />
              <span className="text-gray-700 dark:text-gray-200">
                Tous les employés
              </span>
            </div>
          </MenuItem>
          <MenuItem value="homme">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-gray-700 dark:text-gray-200">
                Employés hommes
              </span>
            </div>
          </MenuItem>
          <MenuItem value="femme">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
              <span className="text-gray-700 dark:text-gray-200">
                Employées femmes
              </span>
            </div>
          </MenuItem>
        </Select>
      </FormControl>

      {/* Filtre par direction desktop */}
      <FormControl size="medium" sx={{ minWidth: 200 }}>
        <Select
          value={directionFilter}
          onChange={(e) => setDirectionFilter(e.target.value)}
          displayEmpty
          sx={{
            borderRadius: "16px",
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            transition: "all 0.3s ease",
            color: "#1f2937",
            "& .MuiOutlinedInput-notchedOutline": {
              border: "none",
            },
            "&:hover": {
              backgroundColor: "#f1f5f9",
              borderColor: "#cbd5e1",
              transform: "translateY(-1px)",
            },
            "&.Mui-focused": {
              backgroundColor: "#ffffff",
              borderColor: "#27aae1",
              boxShadow: "0 0 0 4px rgba(39, 170, 225, 0.1)",
            },
          }}
          className="dark:!bg-gray-700 dark:!border-gray-600 dark:!text-gray-100 dark:hover:!bg-gray-600 dark:focus-within:!bg-gray-700"
        >
          <MenuItem value="all">
            <div className="flex items-center gap-2">
              <Building2
                size={16}
                className="text-gray-500 dark:text-gray-400"
              />
              <span className="text-gray-700 dark:text-gray-200">
                Toutes les directions
              </span>
            </div>
          </MenuItem>
          {availableDirections.map((direction) => (
            <MenuItem key={direction} value={direction}>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-gray-700 dark:text-gray-200 text-sm">
                  {direction}
                </span>
              </div>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Bouton d'ajout desktop */}
      <button
        onClick={onAddEmployee}
        className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-6 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3 whitespace-nowrap group"
      >
        <Plus
          size={20}
          className="group-hover:rotate-90 transition-transform duration-300"
        />
        <span className="hidden md:inline">Ajouter un employé</span>
        <span className="md:hidden">Ajouter</span>
      </button>
    </div>
  );
};

export default EmployeesSearchControls;
