import {
  Control,
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
} from "react-hook-form";
import { EmployerFormData } from "../../../../shared/schemas/EmployerSchema";
import { sexe_enum, status_administratif_enum } from "@/domain/models/enums";
import {
  Briefcase,
  CalendarIcon,
  Camera,
  ScanEye,
  User,
  WorkflowIcon,
} from "lucide-react";
import FormField from "@/presentation/components/common/ui/FormField";
import { UPLOAD_CONFIG } from "@/shared/constants/cabinetMedicalConfig";
import { useCallback, useEffect, useRef, useState } from "react";
import DateField from "@/presentation/components/common/ui/DateField";
import { useToast } from "@/presentation/hooks/use-toast";
import dayjs from "dayjs";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer.ts";
import { Employer } from "@/domain/models/Employer.ts";

interface EmployeeFormProps {
  control: Control<EmployerFormData>;
  employed?: Employer;
  onSubmit: () => Promise<void>;
  register: UseFormRegister<EmployerFormData>;
  errors: FieldErrors<EmployerFormData>;
  setValue: UseFormSetValue<EmployerFormData>;
  profilePhotoFile: File | null;
  setProfilePhotoFile: React.Dispatch<React.SetStateAction<File>>;
}

const EmployeeForm = ({
  onSubmit,
  register,
  errors,
  control,
  setValue,
  profilePhotoFile,
  setProfilePhotoFile,
  employed = null,
}: EmployeeFormProps) => {
  const { selectedEmployerSlice } = useEmployer();

  const [previewUrl, setPreviewUrl] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const toast = useToast();
  const tomorrow = dayjs().add(1, "day").toDate();
  const minLegalYearsEmployees = dayjs().add(-18, "years").toDate();

  const triggerFileInput = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Synchroniser le previewUrl local avec celui du formulaire
  useEffect(() => {
    if (previewUrl) {
      setPreviewUrl(previewUrl);
    }
  }, [previewUrl]);

  // Initialisation avec valuer par defaut du photo de profile
  useEffect(() => {
    if (!employed) {
      setValue("photo", "");
      setValue("date_de_naissance", minLegalYearsEmployees);
      setValue("date_entree_en_fonction", tomorrow);
      return;
    }

    // Initialisation des donne en mode edition d'employer
    setValue("photo", employed.photo || "");
    setValue("date_de_naissance", employed.date_de_naissance);
    setValue("date_entree_en_fonction", employed.date_entree_en_fonction);
    setValue("nom", employed.nom);
    setValue("prenom", employed.prenom);
    setValue("matricule", employed.matricule);
    setValue("fonction", employed.fonction);
    setValue("status_administratif", employed.status_administratif);
    setValue("sexe", employed.sexe);
    setValue("direction", employed.direction);
  }, []);

  const handleImageUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) {
        setValue("photo", "");
        setProfilePhotoFile(null);
        return;
      }

      const maxSizeBytes = UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB * 1024 * 1024;
      if (file.size > maxSizeBytes) {
        toast.error(
          `L'image (${(file.size / 1024 / 1024).toFixed(
            2
          )}MB) dépasse la limite de ${UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB}MB`
        );
        return;
      }

      // Créer une URL pour la prévisualisation
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      setValue("photo", objectUrl, { shouldValidate: true });
      setProfilePhotoFile(file);
    },
    [previewUrl, toast]
  );

  return (
    <form onSubmit={onSubmit}>
      <div className="flex flex-col gap-3">
        <div className="flex flex-col items-center mb-12">
          <p className="m-4 mb-4 text-center text-[#07294A] dark:text-gray-200 font-medium text-sm sm:text-base">
            Photo de profil (optionnelle)
          </p>
          <p className="mb-4 text-center text-gray-500 dark:text-gray-400 text-xs sm:text-sm">
            Taille maximale: {UPLOAD_CONFIG.MAX_IMAGE_SIZE_MB}MB
          </p>
          <div className="relative">
            <div className="w-[100px] h-[100px] sm:w-[120px] sm:h-[120px] rounded-full border-2 border-[#e0e0e0] dark:border-gray-600 overflow-hidden bg-gray-200 dark:bg-gray-700 flex flex-col justify-center items-center align-center">
              {previewUrl ? (
                <img
                  src={previewUrl}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                <User size={60} className="text-gray-400 dark:text-gray-500" />
              )}
            </div>
            <button
              onClick={triggerFileInput}
              className="absolute bottom-0 right-0 bg-meddoc-primary hover:bg-[#0056b3] dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-full w-9 h-9 flex items-center justify-center transition-colors"
              type="button"
            >
              <Camera size={18} />
            </button>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleImageUpload}
              accept="image/*"
              className="hidden"
            />
          </div>
        </div>

        <FormField
          id="nom"
          label="Nom"
          placeholder="Entrez le nom de l'employer"
          icon={User}
          register={register}
          required
          error={errors.nom}
          validation={{
            required: "Le nom de l'employer est requis",
            minLength: {
              value: 2,
              message: "Le nom doit contenir au moins 2 caractères",
            },
          }}
        />

        <FormField
          id="prenom"
          label="Prenom"
          placeholder="Entrez le prenom de l'employer"
          icon={User}
          register={register}
          required
          error={errors.prenom}
          validation={{
            required: "Le prenom de l'employer est requis",
            minLength: {
              value: 2,
              message: "Le prenom doit contenir au moins 2 caractères",
            },
          }}
        />

        <DateField
          id="date_de_naissance"
          label="Date de naissance"
          icon={CalendarIcon}
          defaultValue={minLegalYearsEmployees}
          control={control}
          error={errors.date_de_naissance}
          required
          description="Format: JJ/MM/AAAA"
        />

        <FormField
          id="matricule"
          label="Matricule"
          placeholder="Entrez le matricule de l'employer"
          icon={ScanEye}
          register={register}
          required
          error={errors.matricule}
          validation={{
            required: "Le matricule de l'employer est requis",
            minLength: {
              value: 2,
              message: "Le matricule doit contenir au moins 2 caractères",
            },
          }}
        />

        <FormField
          id="direction"
          label="Direction"
          placeholder="Entrez la direction de l'employer"
          icon={WorkflowIcon}
          register={register}
          required
          error={errors.direction}
          validation={{
            required: "La direction de l'employer est requis",
            minLength: {
              value: 2,
              message: "La direction doit contenir au moins 2 caractères",
            },
          }}
        />

        <FormField
          id="fonction"
          label="Fonction"
          placeholder="Entrez la fonction de l'employer"
          icon={Briefcase}
          register={register}
          required
          error={errors.fonction}
          validation={{
            required: "La fonction de l'employer est requis",
            minLength: {
              value: 2,
              message: "La fonction doit contenir au moins 2 caractères",
            },
          }}
        />

        <DateField
          id="date_entree_en_fonction"
          label="Date d'entree en fonction"
          icon={CalendarIcon}
          defaultValue={tomorrow}
          minDate={tomorrow}
          control={control}
          error={errors.date_entree_en_fonction}
          description="Format: JJ/MM/AAAA"
        />

        <FormField
          id="status_administratif"
          label="Status administratif"
          placeholder="Sélectionnez le status administratif"
          type="select"
          icon={User}
          register={register}
          required
          error={errors.sexe}
          validation={{
            required: "Le sexe est requis",
          }}
          options={[
            { value: status_administratif_enum.PERMANENT, label: "Permanent" },
            {
              value: status_administratif_enum.CONTRACTUEL,
              label: "Contractuel",
            },
            {
              value: status_administratif_enum.TEMPORAIRE,
              label: "Temporaire",
            },
          ]}
          className="col-span-1 sm:col-span-1"
        />

        <FormField
          id="sexe"
          label="Sexe"
          placeholder="Sélectionnez votre sexe"
          type="select"
          icon={User}
          register={register}
          required
          error={errors.sexe}
          validation={{
            required: "Le sexe est requis",
          }}
          options={[
            { value: sexe_enum.homme, label: "Homme" },
            { value: sexe_enum.femme, label: "Femme" },
          ]}
          className="col-span-1 sm:col-span-1"
        />
      </div>
    </form>
  );
};

export default EmployeeForm;
