import { Box, Typography } from "@mui/material";
import EmployerCard from "@/presentation/components/features/professional/employer/EmployerCard";
import { Employer } from "@/domain/models/Employer";

/**
 * Props pour le composant EmployeesGrid
 */
interface EmployeesGridProps {
  /** Liste des employés filtrés à afficher */
  employees: Employer[];
  /** Requête de recherche actuelle */
  searchQuery: string;
}

/**
 * Composant qui affiche la grille des employés avec gestion de l'état vide
 *
 * @param props - Les propriétés du composant
 * @returns JSX.Element
 *
 * @example
 * ```tsx
 * <EmployeesGrid
 *   employees={filteredEmployees}
 *   searchQuery={searchQuery}
 * />
 * ```
 */
const EmployeesGrid = ({ employees, searchQuery }: EmployeesGridProps) => {
  return (
    <div className="mb-4">
      <Typography
        sx={{
          fontWeight: "bold",
          color: "#2c3e50",
          mb: 3,
        }}
        className="dark:!text-gray-100"
      >
        Employés ({employees.length})
      </Typography>

      {employees.length === 0 ? (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          py={8}
          sx={{
            backgroundColor: "rgba(0, 0, 0, 0.02)",
            borderRadius: 2,
            border: "1px dashed rgba(0, 0, 0, 0.12)",
          }}
          className="dark:!bg-white/5 dark:!border-white/20"
        >
          <Typography
            variant="h6"
            sx={{
              color: "#6b7280",
            }}
            className="dark:!text-gray-400"
            gutterBottom
          >
            Aucun employé trouvé
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: "#6b7280",
            }}
            className="dark:!text-gray-400"
            textAlign="center"
          >
            {searchQuery
              ? "Aucun employé ne correspond à votre recherche"
              : "Vous n'avez aucun employé pour le moment"}
          </Typography>
        </Box>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {employees.map((employee) => (
            <div style={{ cursor: "pointer" }} key={employee.id}>
              <EmployerCard employed={employee} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EmployeesGrid;
