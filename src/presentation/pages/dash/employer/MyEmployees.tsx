import { useEffect, useMemo, useState } from "react";
import {
  Box,
  TextField,
  Card,
  CardContent,
  Typography,
  FormControl,
  Select,
  MenuItem,
  InputAdornment,
} from "@mui/material";
import {
  Plus,
  Users,
  User,
  Filter,
  Search,
  Briefcase,
  Building2,
} from "lucide-react";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useNavigate } from "react-router-dom";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import EmployerCard from "@/presentation/components/features/professional/employer/EmployerCard";
import AddEmployedModal from "@/presentation/components/common/Modal/AddEmployedModal";
import { EditEmployedModal } from "@/presentation/components/common/Modal/EditEmployedModal.tsx";
import { sexe_enum } from "@/domain/models/enums";
import MyEmployeesSearchBar from "./MyEmployeesSearchBar.tsx";
import MyEmployeesMobileControl from "./EmployeesMobileControl.tsx";

const MyEmployees = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [totalEmployeesMale, setTotalEmployeesMale] = useState(0);
  const [totalEmployeesFemale, setTotalEmployeesFemale] = useState(0);
  const [genderFilter, setGenderFilter] = useState<"all" | "homme" | "femme">(
    "all"
  );
  const [directionFilter, setDirectionFilter] = useState<string>("all");
  const dashId = useAppSelector((state) => state.authentification.user?.id);
  const [isAddEmployeeFormOpen, setIsAddEmployeeFormOpen] =
    useState<boolean>(false);

  const { employers, getAll } = useEmployer();

  const handleAddEmployedOpen = () => {
    setIsAddEmployeeFormOpen(true);
  };

  // Obtenir la liste unique des directions
  const availableDirections = useMemo(() => {
    if (!employers) return [];
    const directions = employers.map((employee) => employee.direction);
    return [...new Set(directions)].sort();
  }, [employers]);

  // filtrer employees
  const filteredEmployees = useMemo(() => {
    return (
      employers?.filter((employee) => {
        const matchesSearch =
          employee.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
          employee.prenom.toLowerCase().includes(searchQuery.toLowerCase()) ||
          employee.matricule
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          employee.direction.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesGender =
          genderFilter === "all" || employee.sexe === genderFilter;

        const matchesDirection =
          directionFilter === "all" || employee.direction === directionFilter;

        return matchesSearch && matchesGender && matchesDirection;
      }) || []
    );
  }, [employers, searchQuery, genderFilter, directionFilter]);

  useEffect(() => {
    if (dashId) {
      getAll(dashId);
    }
  }, [dashId]);

  useEffect(() => {
    if (filteredEmployees) {
      const totalMale = filteredEmployees.filter(
        (employee) => employee.sexe === sexe_enum.homme
      ).length;
      const totalFemale = filteredEmployees.filter(
        (employee) => employee.sexe === sexe_enum.femme
      ).length;
      setTotalEmployeesMale(totalMale);
      setTotalEmployeesFemale(totalFemale);
    }
  }, [filteredEmployees]);

  const navigate = useNavigate();

  return (
    <Box className="w-full">
      {/* Barre de recherche et contrôles modernes */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 sm:p-6 mb-6 transition-colors duration-200">
        {/* Version mobile - layout vertical */}
        <div className="block sm:hidden space-y-4">
          {/* Barre de recherche mobile */}
          <MyEmployeesSearchBar
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
          />

          {/* Contrôles mobile */}
          <MyEmployeesMobileControl
            genderFilter={genderFilter}
            setGenderFilter={setGenderFilter}
            directionFilter={directionFilter}
            setDirectionFilter={setDirectionFilter}
            availableDirections={availableDirections}
            handleAddEmployedOpen={handleAddEmployedOpen}
          />
        </div>

        {/* Version desktop - layout horizontal */}
        <div className="hidden sm:flex items-center gap-4">
          {/* Barre de recherche desktop */}
          <div className="flex-1 max-w-md">
            <TextField
              fullWidth
              placeholder="Rechercher un employé par nom, prénom ou matricule..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              size="medium"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search
                      size={20}
                      className="text-gray-400 dark:text-gray-500"
                    />
                  </InputAdornment>
                ),
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: "16px",
                  backgroundColor: "#f8fafc",
                  border: "1px solid #e2e8f0",
                  transition: "all 0.3s ease",
                  fontSize: "0.95rem",
                  color: "#1f2937",
                  "& fieldset": {
                    border: "none",
                  },
                  "&:hover": {
                    backgroundColor: "#f1f5f9",
                    borderColor: "#cbd5e1",
                    transform: "translateY(-1px)",
                  },
                  "&.Mui-focused": {
                    backgroundColor: "#ffffff",
                    borderColor: "#27aae1",
                    boxShadow: "0 0 0 4px rgba(39, 170, 225, 0.1)",
                    transform: "translateY(-1px)",
                  },
                },
                "& .MuiInputBase-input": {
                  "&::placeholder": {
                    color: "#9ca3af",
                    opacity: 1,
                  },
                },
              }}
              className="[&_.MuiOutlinedInput-root]:dark:!bg-gray-700 [&_.MuiOutlinedInput-root]:dark:!border-gray-600 [&_.MuiOutlinedInput-root]:dark:!text-gray-100 [&_.MuiOutlinedInput-root:hover]:dark:!bg-gray-600 [&_.MuiOutlinedInput-root.Mui-focused]:dark:!bg-gray-700 [&_.MuiInputBase-input::placeholder]:dark:!text-gray-400"
            />
          </div>

          {/* Filtre par genre desktop */}
          <FormControl size="medium" sx={{ minWidth: 140 }}>
            <Select
              value={genderFilter}
              onChange={(e) =>
                setGenderFilter(e.target.value as "all" | "homme" | "femme")
              }
              displayEmpty
              sx={{
                borderRadius: "16px",
                backgroundColor: "#f8fafc",
                border: "1px solid #e2e8f0",
                transition: "all 0.3s ease",
                color: "#1f2937",
                "& .MuiOutlinedInput-notchedOutline": {
                  border: "none",
                },
                "&:hover": {
                  backgroundColor: "#f1f5f9",
                  borderColor: "#cbd5e1",
                  transform: "translateY(-1px)",
                },
                "&.Mui-focused": {
                  backgroundColor: "#ffffff",
                  borderColor: "#27aae1",
                  boxShadow: "0 0 0 4px rgba(39, 170, 225, 0.1)",
                },
              }}
              className="dark:!bg-gray-700 dark:!border-gray-600 dark:!text-gray-100 dark:hover:!bg-gray-600 dark:focus-within:!bg-gray-700"
            >
              <MenuItem value="all">
                <div className="flex items-center gap-2">
                  <Filter
                    size={16}
                    className="text-gray-500 dark:text-gray-400"
                  />
                  <span className="text-gray-700 dark:text-gray-200">
                    Tous les employés
                  </span>
                </div>
              </MenuItem>
              <MenuItem value="homme">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-200">
                    Employés hommes
                  </span>
                </div>
              </MenuItem>
              <MenuItem value="femme">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-200">
                    Employées femmes
                  </span>
                </div>
              </MenuItem>
            </Select>
          </FormControl>

          {/* Filtre par direction desktop */}
          <FormControl size="medium" sx={{ minWidth: 200 }}>
            <Select
              value={directionFilter}
              onChange={(e) => setDirectionFilter(e.target.value)}
              displayEmpty
              sx={{
                borderRadius: "16px",
                backgroundColor: "#f8fafc",
                border: "1px solid #e2e8f0",
                transition: "all 0.3s ease",
                color: "#1f2937",
                "& .MuiOutlinedInput-notchedOutline": {
                  border: "none",
                },
                "&:hover": {
                  backgroundColor: "#f1f5f9",
                  borderColor: "#cbd5e1",
                  transform: "translateY(-1px)",
                },
                "&.Mui-focused": {
                  backgroundColor: "#ffffff",
                  borderColor: "#27aae1",
                  boxShadow: "0 0 0 4px rgba(39, 170, 225, 0.1)",
                },
              }}
              className="dark:!bg-gray-700 dark:!border-gray-600 dark:!text-gray-100 dark:hover:!bg-gray-600 dark:focus-within:!bg-gray-700"
            >
              <MenuItem value="all">
                <div className="flex items-center gap-2">
                  <Building2
                    size={16}
                    className="text-gray-500 dark:text-gray-400"
                  />
                  <span className="text-gray-700 dark:text-gray-200">
                    Toutes les directions
                  </span>
                </div>
              </MenuItem>
              {availableDirections.map((direction) => (
                <MenuItem key={direction} value={direction}>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-gray-700 dark:text-gray-200 text-sm">
                      {direction}
                    </span>
                  </div>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Bouton d'ajout desktop */}
          <button
            onClick={handleAddEmployedOpen}
            className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-6 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3 whitespace-nowrap group"
          >
            <Plus
              size={20}
              className="group-hover:rotate-90 transition-transform duration-300"
            />
            <span className="hidden md:inline">Ajouter un employé</span>
            <span className="md:hidden">Ajouter</span>
          </button>
        </div>
      </div>

      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {/* Total Employés */}
        <Card
          sx={{
            borderRadius: "8px",
            boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
            backgroundColor: "white",
          }}
          className="dark:!bg-gray-800 dark:!shadow-lg"
        >
          <CardContent sx={{ p: 1.5, "&:last-child": { pb: 1.5 } }}>
            <div className="flex items-center justify-between">
              <div>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: "0.7rem",
                    lineHeight: 1,
                    color: "#6b7280",
                  }}
                  className="dark:!text-gray-400"
                >
                  TOTAL EMPLOYÉS
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: "bold",
                    color: "#2c3e50",
                    mt: 0.25,
                  }}
                  className="dark:!text-gray-100"
                >
                  {filteredEmployees.length}
                </Typography>
              </div>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: "8px",
                  backgroundColor: "#4285f4",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 2px 8px rgba(66, 133, 244, 0.3)",
                }}
              >
                <Briefcase size={16} color="white" />
              </Box>
            </div>
          </CardContent>
        </Card>

        {/* Hommes */}
        <Card
          sx={{
            borderRadius: "8px",
            boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
            backgroundColor: "white",
          }}
          className="dark:!bg-gray-800 dark:!shadow-lg"
        >
          <CardContent sx={{ p: 1.5, "&:last-child": { pb: 1.5 } }}>
            <div className="flex items-center justify-between">
              <div>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: "0.7rem",
                    lineHeight: 1,
                    color: "#6b7280",
                  }}
                  className="dark:!text-gray-400"
                >
                  HOMMES
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: "bold",
                    color: "#2c3e50",
                    mt: 0.25,
                  }}
                  className="dark:!text-gray-100"
                >
                  {totalEmployeesMale}
                </Typography>
              </div>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: "8px",
                  backgroundColor: "#6c5ce7",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 2px 8px rgba(108, 92, 231, 0.3)",
                }}
              >
                <User size={16} color="white" />
              </Box>
            </div>
          </CardContent>
        </Card>

        {/* Femmes */}
        <Card
          sx={{
            borderRadius: "8px",
            boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
            backgroundColor: "white",
          }}
          className="dark:!bg-gray-800 dark:!shadow-lg"
        >
          <CardContent sx={{ p: 1.5, "&:last-child": { pb: 1.5 } }}>
            <div className="flex items-center justify-between">
              <div>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: "0.7rem",
                    lineHeight: 1,
                    color: "#6b7280",
                  }}
                  className="dark:!text-gray-400"
                >
                  FEMMES
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: "bold",
                    color: "#2c3e50",
                    mt: 0.25,
                  }}
                  className="dark:!text-gray-100"
                >
                  {totalEmployeesFemale}
                </Typography>
              </div>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: "8px",
                  backgroundColor: "#fd79a8",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 2px 8px rgba(253, 121, 168, 0.3)",
                }}
              >
                <User size={16} color="white" />
              </Box>
            </div>
          </CardContent>
        </Card>

        {/* Directions */}
        <Card
          sx={{
            borderRadius: "8px",
            boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
            backgroundColor: "white",
          }}
          className="dark:!bg-gray-800 dark:!shadow-lg"
        >
          <CardContent sx={{ p: 1.5, "&:last-child": { pb: 1.5 } }}>
            <div className="flex items-center justify-between">
              <div>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: "0.7rem",
                    lineHeight: 1,
                    color: "#6b7280",
                  }}
                  className="dark:!text-gray-400"
                >
                  DIRECTIONS
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: "bold",
                    color: "#2c3e50",
                    mt: 0.25,
                  }}
                  className="dark:!text-gray-100"
                >
                  {availableDirections.length}
                </Typography>
              </div>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: "8px",
                  backgroundColor: "#10b981",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 2px 8px rgba(16, 185, 129, 0.3)",
                }}
              >
                <Building2 size={16} color="white" />
              </Box>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des employés */}
      <div className="mb-4">
        <Typography
          sx={{
            fontWeight: "bold",
            color: "#2c3e50",
            mb: 3,
          }}
          className="dark:!text-gray-100"
        >
          Employés ({filteredEmployees.length})
        </Typography>

        {filteredEmployees.length === 0 ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={8}
            sx={{
              backgroundColor: "rgba(0, 0, 0, 0.02)",
              borderRadius: 2,
              border: "1px dashed rgba(0, 0, 0, 0.12)",
            }}
            className="dark:!bg-white/5 dark:!border-white/20"
          >
            <Typography
              variant="h6"
              sx={{
                color: "#6b7280",
              }}
              className="dark:!text-gray-400"
              gutterBottom
            >
              Aucun employé trouvé
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "#6b7280",
              }}
              className="dark:!text-gray-400"
              textAlign="center"
            >
              {searchQuery
                ? "Aucun employé ne correspond à votre recherche"
                : "Vous n'avez aucun employé pour le moment"}
            </Typography>
          </Box>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredEmployees.map((employee) => (
              <div style={{ cursor: "pointer" }} key={employee.id}>
                <EmployerCard employed={employee} />
              </div>
            ))}
          </div>
        )}
      </div>

      {isAddEmployeeFormOpen && (
        <AddEmployedModal
          isOpen={isAddEmployeeFormOpen}
          setIsOpen={setIsAddEmployeeFormOpen}
        />
      )}
    </Box>
  );
};

export default MyEmployees;
