import { useEffect, useMemo, useState } from "react";
import { Box } from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";

import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import AddEmployedModal from "@/presentation/components/common/Modal/AddEmployedModal";
import { sexe_enum } from "@/domain/models/enums";
import MyEmployeesSearchBar from "./MyEmployeesSearchBar.tsx";
import MyEmployeesMobileControl from "./EmployeesMobileControl.tsx";
import EmployeesSearchControls from "./EmployeesSearchControls.tsx";
import EmployeesStatsCards from "./EmployeesStatsCards.tsx";
import EmployeesGrid from "./EmployeesGrid.tsx";

const MyEmployees = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [totalEmployeesMale, setTotalEmployeesMale] = useState(0);
  const [totalEmployeesFemale, setTotalEmployeesFemale] = useState(0);
  const [genderFilter, setGenderFilter] = useState<"all" | "homme" | "femme">(
    "all"
  );
  const [directionFilter, setDirectionFilter] = useState<string>("all");
  const dashId = useAppSelector((state) => state.authentification.user?.id);
  const [isAddEmployeeFormOpen, setIsAddEmployeeFormOpen] =
    useState<boolean>(false);

  const { employers, getAll } = useEmployer();

  const handleAddEmployedOpen = () => {
    setIsAddEmployeeFormOpen(true);
  };

  // Obtenir la liste unique des directions
  const availableDirections = useMemo(() => {
    if (!employers) return [];
    const directions = employers.map((employee) => employee.direction);
    return [...new Set(directions)].sort();
  }, [employers]);

  // filtrer employees
  const filteredEmployees = useMemo(() => {
    return (
      employers?.filter((employee) => {
        const matchesSearch =
          employee.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
          employee.prenom.toLowerCase().includes(searchQuery.toLowerCase()) ||
          employee.matricule
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          employee.direction.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesGender =
          genderFilter === "all" || employee.sexe === genderFilter;

        const matchesDirection =
          directionFilter === "all" || employee.direction === directionFilter;

        return matchesSearch && matchesGender && matchesDirection;
      }) || []
    );
  }, [employers, searchQuery, genderFilter, directionFilter]);

  useEffect(() => {
    if (dashId) {
      getAll(dashId);
    }
  }, [dashId, getAll]);

  useEffect(() => {
    if (filteredEmployees) {
      const totalMale = filteredEmployees.filter(
        (employee) => employee.sexe === sexe_enum.homme
      ).length;
      const totalFemale = filteredEmployees.filter(
        (employee) => employee.sexe === sexe_enum.femme
      ).length;
      setTotalEmployeesMale(totalMale);
      setTotalEmployeesFemale(totalFemale);
    }
  }, [filteredEmployees]);

  return (
    <Box className="w-full">
      {/* Barre de recherche et contrôles modernes */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 sm:p-6 mb-6 transition-colors duration-200">
        {/* Version mobile - layout vertical */}
        <div className="block sm:hidden space-y-4">
          {/* Barre de recherche mobile */}
          <MyEmployeesSearchBar
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
          />

          {/* Contrôles mobile */}
          <MyEmployeesMobileControl
            genderFilter={genderFilter}
            setGenderFilter={setGenderFilter}
            directionFilter={directionFilter}
            setDirectionFilter={setDirectionFilter}
            availableDirections={availableDirections}
            handleAddEmployedOpen={handleAddEmployedOpen}
          />
        </div>

        {/* Version desktop - layout horizontal */}
        <EmployeesSearchControls
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          genderFilter={genderFilter}
          setGenderFilter={setGenderFilter}
          directionFilter={directionFilter}
          setDirectionFilter={setDirectionFilter}
          availableDirections={availableDirections}
          onAddEmployee={handleAddEmployedOpen}
        />
      </div>

      {/* Cartes de statistiques */}
      <EmployeesStatsCards
        totalEmployees={filteredEmployees.length}
        totalEmployeesMale={totalEmployeesMale}
        totalEmployeesFemale={totalEmployeesFemale}
        totalDirections={availableDirections.length}
      />

      {/* Liste des employés */}
      <EmployeesGrid employees={filteredEmployees} searchQuery={searchQuery} />

      {isAddEmployeeFormOpen && (
        <AddEmployedModal
          isOpen={isAddEmployeeFormOpen}
          setIsOpen={setIsAddEmployeeFormOpen}
        />
      )}
    </Box>
  );
};

export default MyEmployees;
