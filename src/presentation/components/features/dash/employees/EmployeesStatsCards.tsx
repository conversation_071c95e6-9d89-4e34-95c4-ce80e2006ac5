import { Briefcase, User, Building2 } from "lucide-react";
import StatCard from "./StatCard";

/**
 * Props pour le composant EmployeesStatsCards
 */
interface EmployeesStatsCardsProps {
  /** Nombre total d'employés filtrés */
  totalEmployees: number;
  /** Nombre d'employés hommes */
  totalEmployeesMale: number;
  /** Nombre d'employées femmes */
  totalEmployeesFemale: number;
  /** Nombre de directions disponibles */
  totalDirections: number;
}

/**
 * Composant qui affiche les cartes de statistiques des employés
 * 
 * @param props - Les propriétés du composant
 * @returns JSX.Element
 * 
 * @example
 * ```tsx
 * <EmployeesStatsCards
 *   totalEmployees={25}
 *   totalEmployeesMale={15}
 *   totalEmployeesFemale={10}
 *   totalDirections={5}
 * />
 * ```
 */
const EmployeesStatsCards = ({
  totalEmployees,
  totalEmployeesMale,
  totalEmployeesFemale,
  totalDirections,
}: EmployeesStatsCardsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      {/* Total Employés */}
      <StatCard
        title="TOTAL EMPLOYÉS"
        value={totalEmployees}
        icon={Briefcase}
        iconBackgroundColor="#4285f4"
        iconShadowColor="rgba(66, 133, 244, 0.3)"
      />

      {/* Hommes */}
      <StatCard
        title="HOMMES"
        value={totalEmployeesMale}
        icon={User}
        iconBackgroundColor="#6c5ce7"
        iconShadowColor="rgba(108, 92, 231, 0.3)"
      />

      {/* Femmes */}
      <StatCard
        title="FEMMES"
        value={totalEmployeesFemale}
        icon={User}
        iconBackgroundColor="#fd79a8"
        iconShadowColor="rgba(253, 121, 168, 0.3)"
      />

      {/* Directions */}
      <StatCard
        title="DIRECTIONS"
        value={totalDirections}
        icon={Building2}
        iconBackgroundColor="#10b981"
        iconShadowColor="rgba(16, 185, 129, 0.3)"
      />
    </div>
  );
};

export default EmployeesStatsCards;
