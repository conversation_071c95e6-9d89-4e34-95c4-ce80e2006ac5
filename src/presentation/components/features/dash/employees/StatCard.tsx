import { Card, CardContent, Typography, Box } from "@mui/material";
import { LucideIcon } from "lucide-react";

/**
 * Props pour le composant StatCard
 */
interface StatCardProps {
  /** Titre de la statistique */
  title: string;
  /** Valeur numérique à afficher */
  value: number;
  /** Icône à afficher */
  icon: LucideIcon;
  /** Couleur de fond de l'icône */
  iconBackgroundColor: string;
  /** Couleur de l'ombre de l'icône */
  iconShadowColor: string;
}

/**
 * Composant réutilisable pour afficher une carte de statistique
 * 
 * @param props - Les propriétés du composant
 * @returns JSX.Element
 * 
 * @example
 * ```tsx
 * <StatCard
 *   title="TOTAL EMPLOYÉS"
 *   value={25}
 *   icon={Briefcase}
 *   iconBackgroundColor="#4285f4"
 *   iconShadowColor="rgba(66, 133, 244, 0.3)"
 * />
 * ```
 */
const StatCard = ({
  title,
  value,
  icon: Icon,
  iconBackgroundColor,
  iconShadowColor,
}: StatCardProps) => {
  return (
    <Card
      sx={{
        borderRadius: "8px",
        boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
        backgroundColor: "white",
      }}
      className="dark:!bg-gray-800 dark:!shadow-lg"
    >
      <CardContent sx={{ p: 1.5, "&:last-child": { pb: 1.5 } }}>
        <div className="flex items-center justify-between">
          <div>
            <Typography
              variant="caption"
              sx={{
                fontSize: "0.7rem",
                lineHeight: 1,
                color: "#6b7280",
              }}
              className="dark:!text-gray-400"
            >
              {title}
            </Typography>
            <Typography
              variant="h5"
              sx={{
                fontWeight: "bold",
                color: "#2c3e50",
                mt: 0.25,
              }}
              className="dark:!text-gray-100"
            >
              {value}
            </Typography>
          </div>
          <Box
            sx={{
              width: 36,
              height: 36,
              borderRadius: "8px",
              backgroundColor: iconBackgroundColor,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxShadow: `0 2px 8px ${iconShadowColor}`,
            }}
          >
            <Icon size={16} color="white" />
          </Box>
        </div>
      </CardContent>
    </Card>
  );
};

export default StatCard;
