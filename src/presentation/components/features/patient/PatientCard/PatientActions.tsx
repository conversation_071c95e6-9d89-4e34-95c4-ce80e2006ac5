import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u<PERSON><PERSON>, <PERSON>witch, FormControlLabel, Chip } from "@mui/material";
import { motion } from "framer-motion";
import { PRIMARY } from "@/shared/constants/Color";
import { MoreVertical, Edit3, Trash2, UserX, User } from "lucide-react";
import { useCarnetDeSanteState } from "@/presentation/hooks/carnetDeSante";

interface PatientActionsProps {
  setIsEditPatientModal: (val: boolean) => void;
  setIsDeletePatientModal: (val: boolean) => void;
  setIsArchivePatientModal: (val: boolean) => void;
  setIsPatientDecedeModal: (val: boolean) => void;
  isPatientDecede: boolean;
}

const PatientActions = ({
  setIsEditPatientModal,
  setIsDeletePatientModal,
  setIsArchivePatientModal,
  setIsPatientDecedeModal,
  isPatientDecede,
}: PatientActionsProps) => {
  const { setIsProfile } = useCarnetDeSanteState();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.checked;
    if (newValue) {
      // Si on active, demander confirmation
      setIsPatientDecedeModal(true);
    }
    handleClose();
  };

  const handleEditProfile = () => {
    setIsEditPatientModal(true);
    handleClose();
  };

  const handleDeletePatient = () => {
    setIsDeletePatientModal(true);
    handleClose();
  };
  const handleArchivePatient = () => {
    setIsArchivePatientModal(true);
    handleClose();
  };

  return (
    <div className="flex items-center gap-2">
      {/* Statut décédé compact */}
      {isPatientDecede && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center"
        >
          <Chip
            label="Décédé"
            size="small"
            sx={{
              backgroundColor: '#ef4444',
              color: 'white',
              fontWeight: 500,
              fontSize: '0.7rem',
              height: '24px',
              '& .MuiChip-label': { px: 1.5 }
            }}
          />
        </motion.div>
      )}

      {/* Switch décédé compact */}
      <Tooltip title={isPatientDecede ? "Patient décédé" : "Marquer comme décédé"}>
        <div className="flex items-center">
          <FormControlLabel
            control={
              <Switch
                checked={isPatientDecede}
                onChange={handleToggle}
                size="small"
                sx={{
                  '& .MuiSwitch-switchBase.Mui-checked': {
                    color: '#ef4444',
                  },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                    backgroundColor: '#ef4444',
                  },
                }}
              />
            }
            label=""
            sx={{ margin: 0 }}
          />
        </div>
      </Tooltip>

      {/* Menu d'actions moderne */}
      <Tooltip title="Actions">
        <IconButton
          onClick={handleClick}
          size="small"
          sx={{
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            color: PRIMARY,
            '&:hover': {
              backgroundColor: 'rgba(59, 130, 246, 0.2)',
              transform: 'scale(1.05)',
            },
            transition: 'all 0.2s ease',
          }}
        >
          <MoreVertical size={18} />
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            borderRadius: 2,
            minWidth: 160,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            border: '1px solid rgba(0,0,0,0.05)',
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem
          onClick={handleEditProfile}
          sx={{
            gap: 1.5,
            py: 1,
            '&:hover': {
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
            }
          }}
        >
          <Edit3 size={16} className="text-blue-500" />
          <span className="text-sm font-medium">Modifier le profil</span>
        </MenuItem>

        <MenuItem
          onClick={handleArchivePatient}
          sx={{
            gap: 1.5,
            py: 1,
            '&:hover': {
              backgroundColor: 'rgba(239, 68, 68, 0.1)',
            }
          }}
        >
          <Trash2 size={16} className="text-red-500" />
          <span className="text-sm font-medium text-red-600">Retirer le patient</span>
        </MenuItem>
      </Menu>
    </div>
  );
};

export default PatientActions;
