import { useState } from "react";
import { <PERSON><PERSON>, Chip, Typography } from "@mui/material";
import { motion } from "framer-motion";
import {
  Phone,
  Mail,
  Calendar,
  Droplet,
  Heart,
  AlertTriangle,
  Shield,
  Activity
} from "lucide-react";
import { DESTRUCTIVE, PRIMARY } from "@/shared/constants/Color";
import PatientActions from "./PatientActions";
import { DeletePatientModal } from "@/presentation/components/common/Modal/DeletePatientModal";
import { ArchivePatientModal } from "@/presentation/components/common/Modal/ArchivePatientModal";
import { DecedePatientModal } from "@/presentation/components/common/Modal/DecedePatientModal";
import Action from "./Action";
import SigneVitauxSection from "./SigneVitauxSection";
import ContactUrgenceSection from "./ContactUrgenceSection";
import { useProfilePatientData } from "@/presentation/hooks/useProfilePatientData";
import { EditPatientModal } from "@/presentation/components/common/Modal/EditPatientModal";

const PatientHeader = () => {
  const [isDeletePatientModal, setIsDeletePatientModal] =
    useState<boolean>(false);
    const [isArchivePatientModal, setIsArchivePatientModal] =
    useState<boolean>(false);
  const [isPatientDecedeModal, setIsPatientDecedeModal] =
    useState<boolean>(false);
  const [isEditPatientModal, setIsEditPatientModal] =
    useState<boolean>(false);

  const handleCloseDeletePatientModal = () => {
    setIsDeletePatientModal(false);
  };

  const handleCloseArchivePatientModal = () => {
    setIsArchivePatientModal(false);
  };
  const handleClosePatientDecedeModal = () => {
    setIsPatientDecedeModal(false);
  };
  const { patientData, professionalPatientId } = useProfilePatientData();

  // Fonction pour calculer l'âge
  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden mb-6"
      >
        {/* Header compact avec gradient */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {/* Avatar compact */}
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
                className="relative"
              >
                <Avatar
                  src={patientData?.avatar}
                  sx={{
                    width: 48,
                    height: 48,
                    border: '2px solid',
                    borderColor: PRIMARY,
                    boxShadow: '0 2px 8px rgba(39, 170, 225, 0.3)'
                  }}
                />
                {/* Indicateur de statut */}
                {!patientData?.decede && (
                  <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
                )}
              </motion.div>

              {/* Informations principales compactes */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <Typography
                    variant="h6"
                    className="text-gray-900 dark:text-white font-semibold truncate"
                    sx={{ fontSize: '1.1rem', lineHeight: 1.2 }}
                  >
                    {patientData?.nom} {patientData?.prenom}
                  </Typography>

                  {/* Badges compacts */}
                  <div className="flex gap-1">
                    {patientData?.decede && (
                      <Chip
                        label="Décédé"
                        size="small"
                        sx={{
                          backgroundColor: DESTRUCTIVE,
                          color: 'white',
                          fontWeight: 500,
                          fontSize: '0.7rem',
                          height: '20px',
                          '& .MuiChip-label': { px: 1 }
                        }}
                      />
                    )}

                    {patientData?.sexe && (
                      <Chip
                        label={patientData.sexe === 'homme' ? 'H' : 'F'}
                        size="small"
                        sx={{
                          backgroundColor: patientData.sexe === 'homme' ? '#3b82f6' : '#ec4899',
                          color: 'white',
                          fontWeight: 500,
                          fontSize: '0.7rem',
                          height: '20px',
                          minWidth: '24px',
                          '& .MuiChip-label': { px: 1 }
                        }}
                      />
                    )}
                  </div>
                </div>

                {/* Métadonnées compactes */}
                <div className="flex items-center gap-3 text-xs text-gray-600 dark:text-gray-300">
                  <span className="flex items-center gap-1">
                    <Shield size={12} className="text-blue-500 dark:text-blue-400" />
                    <span className="font-medium">#{patientData?.unique_id}</span>
                  </span>

                  {patientData?.date_naissance && (
                    <span className="flex items-center gap-1">
                      <Calendar size={12} className="text-green-500 dark:text-green-400" />
                      <span>{calculateAge(patientData.date_naissance instanceof Date ? patientData.date_naissance.toISOString() : patientData.date_naissance)} ans</span>
                    </span>
                  )}

                  {patientData?.groupe_sanguin && (
                    <span className="flex items-center gap-1">
                      <Droplet size={12} className="text-red-500 dark:text-red-400" />
                      <span className="font-medium text-red-600 dark:text-red-400">{patientData.groupe_sanguin}</span>
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Actions compactes */}
            <div className="flex items-center gap-2">
              <PatientActions
                setIsEditPatientModal={setIsEditPatientModal}
                setIsDeletePatientModal={setIsDeletePatientModal}
                setIsArchivePatientModal={setIsArchivePatientModal}
                setIsPatientDecedeModal={setIsPatientDecedeModal}
                isPatientDecede={patientData?.decede}
              />
            </div>
          </div>
        </div>

        {/* Contenu principal compact */}
        <div className="p-4">
          {/* Grille d'informations compacte */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {/* Contact compact */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 space-y-2"
            >
              <div className="flex items-center gap-2 mb-2">
                <Phone size={14} className="text-blue-500 dark:text-blue-400" />
                <Typography variant="subtitle2" className="text-gray-700 dark:text-gray-200 font-medium">
                  Contact
                </Typography>
              </div>

              <div className="space-y-1.5 text-xs">
                <div className="flex items-center gap-2">
                  <Phone size={12} className="text-gray-400 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300 truncate">
                    {patientData?.telephone || "Non renseigné"}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail size={12} className="text-gray-400 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300 truncate">
                    {patientData?.email || "Non renseigné"}
                  </span>
                </div>
              </div>
            </motion.div>

            {/* Informations médicales compactes */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 space-y-2"
            >
              <div className="flex items-center gap-2 mb-2">
                <Heart size={14} className="text-red-500 dark:text-red-400" />
                <Typography variant="subtitle2" className="text-gray-700 dark:text-gray-200 font-medium">
                  Médical
                </Typography>
              </div>

              <div className="space-y-1.5 text-xs">
                <div className="flex items-center justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Groupe</span>
                  <span className="font-medium text-red-600 dark:text-red-400">
                    {patientData?.groupe_sanguin || "N/A"}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Donneur</span>
                  <span className={`font-medium text-xs ${patientData?.donneur_sang
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-gray-500 dark:text-gray-400'
                    }`}>
                    {patientData?.donneur_sang !== undefined
                      ? (patientData.donneur_sang ? "Oui" : "Non")
                      : "N/A"
                    }
                  </span>
                </div>
              </div>
            </motion.div>

            {/* Signes vitaux compacts */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3"
            >
              <div className="flex items-center gap-2 mb-2">
                <Activity size={14} className="text-green-500 dark:text-green-400" />
                <Typography variant="subtitle2" className="text-gray-700 dark:text-gray-200 font-medium">
                  Vitaux
                </Typography>
              </div>
              <div className="text-xs">
                <SigneVitauxSection />
              </div>
            </motion.div>

            {/* Contact d'urgence compact */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3"
            >
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle size={14} className="text-orange-500 dark:text-orange-400" />
                <Typography variant="subtitle2" className="text-gray-700 dark:text-gray-200 font-medium">
                  Urgence
                </Typography>
              </div>
              <div className="text-xs">
                <ContactUrgenceSection urgence={patientData?.urgence} />
              </div>
            </motion.div>
          </div>

          {/* Action component intégré */}
          <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
            <Action />
          </div>
        </div>
      </motion.div>

      {/* Modals */}
      <>
        {isEditPatientModal && patientData && (
          <EditPatientModal
            isOpen={isEditPatientModal}
            handleClose={() => setIsEditPatientModal(false)}
            patient={patientData}
          />
        )}
        {isDeletePatientModal && (
          <DeletePatientModal
            isOpen={isDeletePatientModal}
            patient={{
              id: professionalPatientId,
              nom: patientData.nom,
            }}
            handleClose={handleCloseDeletePatientModal}
          />
        )}
        {isArchivePatientModal && (
          <ArchivePatientModal
            isOpen={isArchivePatientModal}
            patient={{
              id: professionalPatientId,
              nom: patientData.nom,
            }}
            handleClose={handleCloseArchivePatientModal}
          />
        )}
        {isPatientDecedeModal && (
          <DecedePatientModal
            isOpen={isPatientDecedeModal}
            nom={patientData.nom}
            handleClose={handleClosePatientDecedeModal}
          />
        )}
      </>
    </>
  );
};

export default PatientHeader;
