import {
  Control,
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
} from "react-hook-form";
import { ConsultationStepFormData } from "@/shared/schemas/ConsultationStepShema";
import { Typo<PERSON>, Button } from "@mui/material";
import { motion } from "framer-motion";
import {
  StyledButton,
  WarningBox2,
  StyledPaper,
  SectionTitle,
} from "@/styles/styleMui/ConsultationSteper";
import ConsultationStep2Register from "./ConsultationStep2Register";
import { useEffect, useState } from "react";
import ConsultationStep2Login from "./ConsultationStep2Login";
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import WarningSumUp from "@/presentation/components/features/patient/consultationStepper/WarningSumUp";
import { ArrowLeft, UserPlus, LogIn, AlertCircle, Shield } from "lucide-react";
import { useAppSelector } from "@/presentation/hooks/redux";
import { RendezVous } from "@/domain/models";

interface ConsultationStep2Props {
  control: Control<ConsultationStepFormData>;
  errors: FieldErrors<ConsultationStepFormData>;
  onSubmit: () => Promise<RendezVous>;
  register: UseFormRegister<ConsultationStepFormData>;
  setValue: UseFormSetValue<ConsultationStepFormData>;
  onNext: (count: number) => void;
}

const ConsultationStep2 = ({ onNext }: ConsultationStep2Props) => {
  const user = useAppSelector((state) => state.authentification?.user);
  const [identification, setIdentification] = useState("");
  const { handleresetAuth } = useConsultationState();

  useEffect(() => {
    if (user && user.id) {
      onNext(1);
    }
  }, [user]);

  return (
    <div>
      {/* Contenu principal */}
      <div className="mt-4">
        <motion.div
          className="lg:col-span-2"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="space-y-6">
            {/* Message d'avertissement */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <WarningBox2>
                <div className="flex items-center gap-3">
                  <AlertCircle size={24} style={{ color: "#f59e0b" }} />
                  <div>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      Confirmation requise
                    </Typography>
                    <Typography variant="body2">
                      Votre RDV n'est pas encore confirmé. Merci de vous
                      identifier pour confirmer votre rendez-vous.
                    </Typography>
                  </div>
                </div>
              </WarningBox2>
            </motion.div>

            {/* Section Inscription */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <StyledPaper>
                <SectionTitle>
                  <UserPlus className="section-icon" />
                  <Typography variant="h6">Nouveau sur MEDDoC ?</Typography>
                </SectionTitle>

                {identification === "S'inscrire" ? (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4 }}
                  >
                    <Typography
                      variant="body1"
                      sx={{ color: "#6b7280", mb: 3 }}
                    >
                      Saisissez vos informations pour continuer.
                    </Typography>
                    <ConsultationStep2Register />
                  </motion.div>
                ) : (
                  <div>
                    <Typography
                      variant="body1"
                      sx={{ color: "#6b7280", mb: 3 }}
                    >
                      Créez votre compte MEDDoC pour accéder à tous nos services
                    </Typography>
                    <StyledButton
                      variant="contained"
                      className="w-full"
                      onClick={() => {
                        setIdentification("S'inscrire");
                        handleresetAuth();
                      }}
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        gap: 2,
                      }}
                    >
                      <UserPlus size={20} />
                      S'inscrire
                    </StyledButton>
                  </div>
                )}
              </StyledPaper>
            </motion.div>

            {/* Section Connexion */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <StyledPaper>
                <SectionTitle>
                  <Shield className="section-icon" />
                  <Typography variant="h6">Déjà membre ?</Typography>
                </SectionTitle>

                {identification === "Se connecter" ? (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4 }}
                  >
                    <ConsultationStep2Login />
                  </motion.div>
                ) : (
                  <div>
                    <Typography
                      variant="body1"
                      sx={{ color: "#6b7280", mb: 3 }}
                    >
                      Connectez-vous avec votre compte MEDDoC existant
                    </Typography>
                    <StyledButton
                      variant="contained"
                      className="w-full"
                      onClick={() => {
                        setIdentification("Se connecter");
                        handleresetAuth();
                      }}
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        gap: 2,
                      }}
                    >
                      <LogIn size={20} />
                      Se connecter
                    </StyledButton>
                  </div>
                )}
              </StyledPaper>
            </motion.div>
          </div>
        </motion.div>
        {/* <WarningSumUp /> */}
      </div>
    </div>
  );
};

export default ConsultationStep2;
