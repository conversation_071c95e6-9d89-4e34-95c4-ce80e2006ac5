import {
  Control,
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
} from "react-hook-form";
import { Typography, FormControlLabel, Checkbox, Link } from "@mui/material";
// import { MuiTelInput } from 'mui-tel-input'
import { useConsultationState } from "@/presentation/hooks/consultationMedicale";
import { StyledButton } from "@/styles/styleMui/ConsultationSteper";
import useRegister from "@/presentation/hooks/use-register";
import { RegisterStep1, RegisterStep2 } from "../../registerPatienStepper";
import { usePatientRegistrationLogic } from "@/presentation/hooks/authentification";
import { PublicRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { useLocation } from "react-router-dom";
import { useToast } from "@/presentation/hooks/use-toast.ts";
import { getSelectedTimeSlot } from "@/presentation/hooks/consultationStep/useSelectedTimeSlot";
import { saveToLocalStorage } from "@/presentation/hooks/consultationStep/saveToLocalStorage.ts";
import { CONSULTATION_STEP_DATA } from "@/presentation/hooks/consultationStep/constant.ts";
import { useState } from "react";

const ConsultationStep2Register = () => {
  const location = useLocation();
  const baseUrl = import.meta.env.VITE_APP_URL;
  const [isDisabled, setIsDisabled] = useState(false);

  const {
    control,
    errors,
    register,
    setValue,
    handleSubmit,
    isLoading,
    onSubmit: registerPatient,
  } = usePatientRegistrationLogic();
  const isLoginUser = true;
  const toast = useToast();
  const {
    acceptConditions,
    activeStep,
    handleAcceptConditionsChange,
    handleActiveStepChange,
  } = useConsultationState();

  const onSubmit = async () => {
    const fullUrl = `${baseUrl}${location.pathname}${location.search}${location.hash}`;

    const patient = await registerPatient(isLoginUser, fullUrl);
    if (patient.success) {
      const data = getSelectedTimeSlot();
      const newData = { ...data, isRegister: true };
      saveToLocalStorage(CONSULTATION_STEP_DATA, newData);
      setIsDisabled(true);
      toast.info(
        "Vous devez confirmer votre compte en cliquant sur le lien envoyé par email."
      );
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <RegisterStep1
          control={control}
          errors={errors}
          onSubmit={onSubmit}
          register={register}
          setValue={setValue}
          patient={null}
        />
        <div className="mt-4">
          <RegisterStep2
            control={control}
            errors={errors}
            onSubmit={onSubmit}
            register={register}
            setValue={setValue}
            patient={null}
          />
        </div>
      </form>
      <FormControlLabel
        control={
          <Checkbox
            checked={acceptConditions}
            onChange={(e) => handleAcceptConditionsChange(e.target.checked)}
            color="primary"
          />
        }
        label={
          <Typography variant="body2">
            J'accepte les{" "}
            <Link href="#" color="primary">
              conditions générales d'utilisation
            </Link>{" "}
            de MEDDoC
          </Typography>
        }
        sx={{ mt: 2 }}
      />
      <StyledButton
        variant="contained"
        className="w-full"
        onClick={onSubmit}
        disabled={!acceptConditions && isDisabled}
        loading={isLoading}
      >
        Continuer
      </StyledButton>
    </>
  );
};

export default ConsultationStep2Register;
