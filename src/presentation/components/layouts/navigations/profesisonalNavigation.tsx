import { ProfessionalRoutesNavigations } from "@/shared/constants/AppRoutesNavigation";
import { Navigation } from "@toolpad/core/AppProvider";
import {
  LayoutDashboard,
  Calendar,
  MessageCircle,
  CircleDollarSign,
  CalendarCheck2,
  Database,
  Users,
  UserCheck,
  UserX,
  Trash2,
} from "lucide-react";

export const PROFESSIONAL_NAVIGATION: Navigation = [
  {
    kind: "header",
    title: "Principale",
  },
  {
    segment: ProfessionalRoutesNavigations.DASHBOARD,
    title: "Tableau de bord",
    icon: <LayoutDashboard />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Agenda",
  },
  {
    segment: ProfessionalRoutesNavigations.AGENDA,
    title: "Agenda",
    icon: <Calendar />,
  },
  {
    segment: ProfessionalRoutesNavigations.APPOINTMENTS,
    title: "Mes rendez-vous",
    icon: <CalendarCheck2 />,
  },
  {
    kind: "divider",
  },

  {
    kind: "header",
    title: "Contacts",
  },
  {
    segment: ProfessionalRoutesNavigations.MANAGE_PATIENTS,
    title: "Patients",
    icon: <Users />,
    children: [
      {
        segment: "actif",
        title: "Actifs",
        icon: <UserCheck style={{ color: "#10b981" }} />, // Vert pour actifs
      },
      {
        segment: "decede",
        title: "Décédés",
        icon: <UserX style={{ color: "#ef4444" }} />, // Rouge pour décédés
      },
      {
        segment: "supprimer",
        title: "Supprimés",
        icon: <Trash2 style={{ color: "#f59e0b" }} />, // Orange pour supprimés
      },
    ],
  },
  {
    segment: ProfessionalRoutesNavigations.MESSAGES,
    title: "Messages",
    icon: <MessageCircle />,
  },
  {
    segment: ProfessionalRoutesNavigations.FACTURATION,
    title: "Facturation",
    icon: <CircleDollarSign />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Service",
  },
  {
    segment: ProfessionalRoutesNavigations.STOCK,
    title: "Stock",
    icon: <Database />,
  },
];
