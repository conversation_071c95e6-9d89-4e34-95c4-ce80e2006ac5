import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import useProfessionalEtablisement from "@/presentation/hooks/use-professional-etablishment";
import { Box, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { highlightText } from "@/shared/utils/highlightText";
import useProfessionals from "@/presentation/hooks/use-professionals.ts";
import { ProfessionalCompleteDTO } from "@/domain/DTOS/ProfessionalDTO.ts";

interface EtablishmentsFilterProps {
  isOpen: boolean;
  searchTerm: string;
  handleOptionSelect: (
    id: number,
    nom: string,
    prenom: string,
    titre: string,
    adresse: string,
    specialite: string
  ) => void;
}

const EtablishmentsFilter = ({
  isOpen,
  searchTerm,
  handleOptionSelect,
}: EtablishmentsFilterProps) => {
  const { listes, loading, getProfessionalEtablisementList } =
    useProfessionalEtablisement();
  const { professionals } = useProfessionals();

  const [filteredListes, setFilteredListes] = useState(listes);
  const [matchingProfessionals, setMatchingProfessionals] =
    useState<ProfessionalCompleteDTO | null>(null);

  useEffect(() => {
    if (professionals && professionals.length > 0) {
      const professional = getProfessionalById(listes[0]?.id_professionnel);
      setMatchingProfessionals(professional);
    }
  }, [professionals]);

  const getProfessionalById = (id: number) => {
    return professionals.find((professional) => professional.id === id);
  };

  useEffect(() => {
    if (listes && listes.length === 0) getProfessionalEtablisementList();
  }, []);

  useEffect(() => {
    if (searchTerm && searchTerm.trim() === "") {
      setFilteredListes(listes);
    } else {
      if (!listes || listes.length === 0) {
        toast.error("Aucune specialite disponible");
        setFilteredListes([]);
      } else {
        if (!listes || listes.length === 0) {
          setFilteredListes([]);
        } else {
          const filtered = listes.filter((option) =>
            option.nom_etablissement
              .toLowerCase()
              .includes(searchTerm.toLowerCase())
          );
          setFilteredListes(filtered);
        }
      }
    }
  }, [searchTerm, listes]);

  if (!isOpen || !searchTerm || searchTerm.trim() === "") return null;

  return (
    <Box className="flex-1 max-h-[300px] overflow-y-auto p-2">
      <Typography
        className="text-gray-600 font-medium mb-4 text-sm"
        component={"div"}
      >
        <p className="font-bold text-meddoc-fonce">Établissements</p>
      </Typography>
      <Box className="flex flex-col space-y-2">
        {loading ? (
          <Box className="flex justify-center py-4">
            <LoadingSpinner size={32} color="border-gray-300" />
          </Box>
        ) : filteredListes.length > 0 ? (
          filteredListes.map((option, index) => (
            <Box
              key={index}
              className="p-3 hover:bg-gray-100 cursor-pointer rounded-md transition-colors"
              onClick={() =>
                handleOptionSelect(
                  option.id_professionnel,
                  option.nom_etablissement,
                  matchingProfessionals?.prenom,
                  matchingProfessionals.titre,
                  matchingProfessionals.adresse,
                  matchingProfessionals.specialites_professionnel[0]
                    .nom_specialite
                )
              }
            >
              <Typography className="text-sm">
                {highlightText(option.nom_etablissement, searchTerm)}
              </Typography>
            </Box>
          ))
        ) : (
          <p className="text-xs mt-2 text-gray-500">
            {searchTerm
              ? `Aucun établissement nommé "${searchTerm}" trouvé`
              : "Aucun établissement disponible"}
          </p>
        )}
      </Box>
    </Box>
  );
};

export default EtablishmentsFilter;
