import {
    Dialog,
    DialogTitle,
    DialogContent,
    <PERSON>alogActions,
    IconButton,
    Typography,
    Button,
    Tooltip,
  } from "@mui/material";
  import { X } from "lucide-react";
  import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
  import { PRIMARY } from "@/shared/constants/Color";
  
  interface ArchivePatientModalProps {
    isOpen: boolean;
    handleClose: () => void;
    patient: {
      id: number;
      nom: string;
    };
  }
  
  export const ArchivePatientModal = ({
    isOpen,
    handleClose,
    patient,
  }: ArchivePatientModalProps) => {
    const { loading, handleArchiveProfessionnelPatient } =
      useProfessionnelPatient();
  
    const onArchive = async () => {
      await handleArchiveProfessionnelPatient(patient.id);
      handleClose();
      window.history.back();
    };
  
    return (
      <Dialog open={isOpen} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle className="flex justify-end">
          <Tooltip title="Fermer">
            <IconButton onClick={handleClose} size="small">
              <X className="h-4 w-4" />
            </IconButton>
          </Tooltip>
        </DialogTitle>
        <DialogContent className="flex justify-center">
          <Typography variant="body1">
            Êtes-vous sûr de vouloir spprimer (archiver) {patient.nom} ?
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2, display: "flex", justifyContent: "center" }}>
          <Button
            variant="outlined"
            onClick={handleClose}
            sx={{ textTransform: "none" }}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            onClick={onArchive}
            sx={{ textTransform: "none", backgroundColor: PRIMARY }}
            loading={loading}
          >
            Supprimer et archiver
          </Button>
        </DialogActions>
      </Dialog>
    );
  };
  