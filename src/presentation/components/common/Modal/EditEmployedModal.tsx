import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  IconButton,
  Typography,
  Button,
  Tooltip,
} from "@mui/material";
import { X } from "lucide-react";
import { PRIMARY } from "@/shared/constants/Color";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer.ts";
import EmployeeForm from "@/presentation/pages/dash/employer/EmployeeForm";
import { useState } from "react";
import {
  EmployerFormData,
  employerSchema,
} from "@/presentation/pages/dash/employer/EmployerSchema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Employer } from "@/domain/models/Employer";

interface EditEmployedMOdalProps {
  isOpen: boolean;
  employed: Employer;
  handleCloseModal: () => void;
}

export const EditEmployedModal = ({
  employed,
  isOpen,
  handleCloseModal,
}: EditEmployedMOdalProps) => {
  const { updateEmployer, loading } = useEmployer();
  const [profilePhoto, setProfilePhoto] = useState<File | null>(null);

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<EmployerFormData>({
    resolver: zodResolver(employerSchema),
  });

  const onSubmit = async (data: EmployerFormData) => {
    try {
      console.log("hello world", data);
      const result = await updateEmployer(employed.id, data);

      if (result) {
        handleCloseModal();
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={handleCloseModal}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        className: "dark:bg-gray-800 dark:text-white",
        sx: {
          "&.dark": {
            backgroundColor: "#1f2937",
            color: "#ffffff",
          },
        },
      }}
    >
      <DialogTitle className="flex justify-between items-center bg-gradient-to-r from-meddoc-fonce to-meddoc-primary text-white dark:from-gray-700 dark:to-gray-600">
        <Typography
          variant="h6"
          component="div"
          className="text-center w-full text-white font-semibold"
          sx={{
            color: "white !important",
            fontWeight: 600,
          }}
        >
          Modifier l'employé{" "}
          {`${employed.nom.toUpperCase()} ${employed.prenom}`}
        </Typography>
        <Tooltip title="Fermer">
          <IconButton
            onClick={handleCloseModal}
            size="small"
            sx={{
              color: "white",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              },
            }}
          >
            <X className="h-4 w-4" />
          </IconButton>
        </Tooltip>
      </DialogTitle>
      <DialogContent className="dark:bg-gray-800 dark:text-white">
        <EmployeeForm
          employed={employed}
          control={control}
          errors={errors}
          onSubmit={handleSubmit(onSubmit)}
          profilePhotoFile={profilePhoto}
          setProfilePhotoFile={setProfilePhoto}
          register={register}
          setValue={setValue}
        />
      </DialogContent>
      <DialogActions className="dark:bg-gray-800 dark:border-t dark:border-gray-700">
        <Button
          variant="outlined"
          onClick={handleCloseModal}
          sx={{
            textTransform: "none",
            borderColor: PRIMARY,
            color: PRIMARY,
            "&:hover": {
              borderColor: PRIMARY,
              backgroundColor: `${PRIMARY}10`,
            },
          }}
          className="dark:border-gray-500 dark:text-gray-300 dark:hover:border-gray-400"
        >
          Annuler
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit(onSubmit)}
          disabled={loading}
          className="font-medium bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white dark:text-white"
        >
          {loading ? "Enregistrement..." : "Enregistrer"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
