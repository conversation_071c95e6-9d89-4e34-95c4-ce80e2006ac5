import { Employer } from "@/domain/models/Employer";

/**
 * Interface pour le usecase de récupération des employés par ID utilisateur
 */
export interface IGetEmployersByUserIdUsecase {
  /**
   * Exécute la récupération des employés par ID utilisateur
   * @param id_utilisateur - L'ID de l'utilisateur
   * @returns Promise<Employer[]> - La liste des employés de l'utilisateur
   */
  execute(id_utilisateur: number): Promise<Employer[]>;
}
