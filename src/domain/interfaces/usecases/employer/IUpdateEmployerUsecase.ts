import { Employer } from "@/domain/models/Employer";

/**
 * Interface pour le usecase de mise à jour d'un employé
 */
export interface IUpdateEmployerUsecase {
  /**
   * Exécute la mise à jour d'un employé
   * @param id - L'ID de l'employé à mettre à jour
   * @param employerData - Les données partielles de l'employé à mettre à jour
   * @returns Promise<Employer> - L'employé mis à jour
   */
  execute(id: number, employerData: Partial<Employer>): Promise<Employer>;
}
