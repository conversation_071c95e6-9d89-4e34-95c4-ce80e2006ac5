import { IArchiveProfessionalPatientRepository } from "@/domain/interfaces/repositories/professionnelPatient";
import { IArchiveProfessionalPatientUsecase } from "@/domain/interfaces/usecases/professionnelPatient";
import { ProfessionnelPatient } from "@/domain/models";

export class ArchiveProfessionalPatientUsecase implements IArchiveProfessionalPatientUsecase
{
  constructor(
    private readonly ArchiveProfessionelPatientRepository: IArchiveProfessionalPatientRepository,
  ) {}

  async execute(id: number): Promise<ProfessionnelPatient | null> {
    try {
      return await this.ArchiveProfessionelPatientRepository.execute(id);
    } catch (error) {
      return null;
    }
  }
}
