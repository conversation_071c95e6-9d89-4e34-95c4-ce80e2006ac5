import { IUpdateEmployerRepository } from "@/domain/interfaces/repositories/employer/IUpdateEmployerRepository";
import { Employer } from "@/domain/models/Employer";
import { IUpdateEmployerUsecase } from "@/domain/interfaces/usecases/employer/IUpdateEmployerUsecase";

export class UpdateEmployerUsecase implements IUpdateEmployerUsecase {
  constructor(
    private readonly updateEmployerRepository: IUpdateEmployerRepository
  ) {}

  async execute(
    id: number,
    employerData: Partial<Employer>
  ): Promise<Employer> {
    return this.updateEmployerRepository.execute(id, employerData);
  }
}
