import { IGetEmployerByIdRepository } from "@/domain/interfaces/repositories/employer/IGetEmployerByIdRepository";
import { Employer } from "@/domain/models/Employer";
import { IGetEmployerByIdUsecase } from "@/domain/interfaces/usecases/employer/IGetEmployerByIdUsecase";

export class GetEmployerByIdUsecase implements IGetEmployerByIdUsecase {
  constructor(
    private readonly getEmployerByIdRepository: IGetEmployerByIdRepository
  ) {}

  async execute(id: number): Promise<Employer> {
    return this.getEmployerByIdRepository.execute(id);
  }
}
