import { IGetEmployersByUserIdRepository } from "@/domain/interfaces/repositories/employer/IGetEmployersByUserIdRepository";
import { Employer } from "@/domain/models/Employer";
import { IGetEmployersByUserIdUsecase } from "@/domain/interfaces/usecases/employer/IGetEmployersByUserIdUsecase";

export class GetEmployersByUserIdUsecase
  implements IGetEmployersByUserIdUsecase
{
  constructor(
    private readonly getEmployersByUserIdRepository: IGetEmployersByUserIdRepository
  ) {}

  async execute(id_utilisateur: number): Promise<Employer[]> {
    return this.getEmployersByUserIdRepository.execute(id_utilisateur);
  }
}
