import { ICreateEmployerRepository } from "@/domain/interfaces/repositories/employer/ICreateEmployerRepository";
import { Employer } from "@/domain/models/Employer";
import { ICreateEmployerUsecase } from "@/domain/interfaces/usecases/employer/ICreateEmployerUsecase";

export class CreateEmployerUsecase implements ICreateEmployerUsecase {
  constructor(
    private readonly createEmployerRepository: ICreateEmployerRepository
  ) {}

  async execute(employers: Omit<Employer, "id">): Promise<Employer> {
    return this.createEmployerRepository.execute(employers);
  }
}
