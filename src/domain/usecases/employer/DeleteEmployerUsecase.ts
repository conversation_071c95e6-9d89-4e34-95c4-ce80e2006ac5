import { IDeleteEmployerRepository } from "@/domain/interfaces/repositories/employer/IDeleteEmployerRepository";
import { IDeleteEmployerUsecase } from "@/domain/interfaces/usecases/employer/IDeleteEmployerUsecase";

export class DeleteEmployerUsecase implements IDeleteEmployerUsecase {
  constructor(
    private readonly deleteEmployerRepository: IDeleteEmployerRepository
  ) {}

  async execute(id: number): Promise<void> {
    return this.deleteEmployerRepository.execute(id);
  }
}
