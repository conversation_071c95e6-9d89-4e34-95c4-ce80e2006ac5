export class PublicRoutesNavigation {
  static MAIN_PAGE = "/";
  static FIND_PROFESSIONAL = "medecins/:localization";
  static FIND_PROFESSIONAL_WITH_SPECIALITY =
    "medecins/:localization/:speciality";
  static CONSULTATION_PAGE = "consultation";
  static LOGIN_PAGE = "auth/login";
  static REGISTER_PATIENT_PAGE = "auth/inscription/patient";
  static PROFESSIONAL_ADHESION_REQUEST =
    "auth/inscription/adhesion-professionnel";
  static REGISTER_PROFESSIONAL = "auth/inscription/professionnel/:token";
  static DASH_INVITATION = "auth/inscription/dash/:token";
  static ADHESION_SUCCESS = "auth/inscription/professionnel/adhesion-success";
  static EMAIL_CONFIRMATION_PAGE = "auth/confirmation-email/:email";
  static PROFILE_PAGE = ":specialite/:slug";
  static USER_PROFILE_PAGE = "utilisateur/:id";
  static REGISTER_PATIENT_SUCCESS = "auth/inscription/patient/success";
  static HELP = "assistance";
}
export class AdminRoutesNavigations {
  static DASHBOARD = "admin/dashboard";
  static MANAGE_PROFESSIONALS = "admin/gestion-professionnels";
  static MANAGE_ADHESION_REQUESTS = "admin/gestion-demande-adhesion";
  static MANAGE_PATIENTS = "admin/gestion-patients";
  static HELP = "admin/assistance";
  static DASH = "admin/dash";
}

export class ProfessionalRoutesNavigations {
  static DASHBOARD = "professionnel/dashboard";
  static AGENDA = "professionnel/agenda";
  static APPOINTMENTS = "professionnel/rendez-vous";
  static MANAGE_PATIENTS = "professionnel/mes-patients";
  static MANAGE_PATIENTS_ACTIF = "professionnel/mes-patients/actif";
  static MANAGE_PATIENTS_DECEDE = "professionnel/mes-patients/decede";
  static MANAGE_PATIENTS_SUPPRIMER = "professionnel/mes-patients/supprimer";
  static MANAGE_PATIENTS_PAGE = "professionnel/mes-patients/:id";
  static MANAGE_EMPLOYED_PAGE = "professionnel/mes-employer/:id";
  static MESSAGES = "professionnel/messages";
  static FACTURATION = "professionnel/facturations";
  static HELP = "professionnel/assistance";
  static STOCK = "professionnel/stock";
  static PROFILE = "professionnel/mon-profile";
}

export class PatientRoutesNavigation {
  static DASHBOARD = "patient/dashboard";
  static FIND_DOCTOR = "patient/rechercher-docteur";
  static APPOINTMENTS = "patient/rendez-vous";
  static MEDICAL_ACTIVITY = "patient/suivi-medical";
  static FAMILIES = "patient/proches";
  static MESSAGES = "patient/messages";
  static APPOINTMENT_PAGE = "patient/rendez-vous";
  static HELP = "patient/assistance";
  static PROFILE = "patient/mon-profile";
}

export class DashRoutesNavigation {
  static DASHBOARD = "commune-urbaine-antananarivo/dash/dashboard";
  static EMPLOYER = "commune-urbaine-antananarivo/dash/employer";
  static STOCK = "commune-urbaine-antananarivo/dash/stock";
  static HELP = "commune-urbaine-antananarivo/dash/assistance";
}
