import { Employer } from "@/domain/models";
import { sexe_enum, status_administratif_enum } from "@/domain/models/enums";
import { z } from "zod";

export const employerSchema = z.object({
  nom: z.string().min(1, "Nom requis"),
  prenom: z.string().min(1, "Prénom requis"),
  date_de_naissance: z.coerce.date(),
  matricule: z.string().min(1, "Matricule requis"),
  fonction: z.string().min(1, "Fonction requise"),
  date_entree_en_fonction: z.coerce.date().nullable(),
  status_administratif: z.nativeEnum(status_administratif_enum),
  sexe: z.nativeEnum(sexe_enum),
  photo: z.string(),
  direction: z.string().min(1, "Direction requise"),
});

export type EmployerFormData = z.infer<typeof employerSchema>;
