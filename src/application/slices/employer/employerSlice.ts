import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Employer } from "@/domain/models";
import {
  CreateEmployerRepository,
  GetEmployersByUserIdRepository,
  GetEmployerByIdRepository,
  UpdateEmployerRepository,
  DeleteEmployerRepository,
} from "@/infrastructure/repositories/employer";
import {
  CreateEmployerUsecase,
  GetEmployersByUserIdUsecase,
  GetEmployerByIdUsecase,
  UpdateEmployerUsecase,
  DeleteEmployerUsecase,
} from "@/domain/usecases/employer";
import { PostgrestError } from "@supabase/supabase-js";

interface EmplyerSliceState {
  employers: Employer[];
  selectedEmplyerSlice: Employer | null;
  loading: boolean;
  error: string | null;
}

const initialState: EmplyerSliceState = {
  employers: [],
  selectedEmplyerSlice: null,
  loading: false,
  error: null,
};

export const createEmployerThunk = createAsyncThunk<
  Employer, // En cas de succes
  { data: Omit<Employer, "id">; profilePhoto: File | null }, // parametre
  { rejectValue: string } // en cas d'erreur
>("employer/create", async ({ data, profilePhoto }, { rejectWithValue }) => {
  try {
    const createEmployerRepository = new CreateEmployerRepository();
    const createEmployerUsecase = new CreateEmployerUsecase(
      createEmployerRepository
    );
    const result = await createEmployerUsecase.execute(data);
    return result;
  } catch (error) {
    const formatedError = error as PostgrestError;

    /* NOTE: Dans le cas ou on a besoin de gerer une autre code d'erreur
     * avec des messages personnalisees, on peut l'ajouter ici
     */
    switch (formatedError.code) {
      case "23505":
        return rejectWithValue("Matricule deja existant.");
      default:
        return rejectWithValue(formatedError.message);
    }
  }
});

export const getEmployerByIdThunk = createAsyncThunk(
  "employer/getById",
  async (id: number, { rejectWithValue }) => {
    try {
      const getEmployerByIdRepository = new GetEmployerByIdRepository();
      const getemployerUsecase = new GetEmployerByIdUsecase(
        getEmployerByIdRepository
      );
      const result = await getemployerUsecase.execute(id);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const getEmployersByProfessionalIdThunk = createAsyncThunk(
  "employer/getemployer",
  async (userId: number, { rejectWithValue }) => {
    try {
      const getEmployersByUserIdRepository =
        new GetEmployersByUserIdRepository();
      const getemployerUsecase = new GetEmployersByUserIdUsecase(
        getEmployersByUserIdRepository
      );
      const result = await getemployerUsecase.execute(userId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateEmployerThunk = createAsyncThunk<
  Employer, // En cas de succes
  { id: number; data: Partial<Employer> }, // parametre
  { rejectValue: string } // en cas d'erreur
>("employer/update", async ({ id, data }, { rejectWithValue }) => {
  try {
    const updateEmployerRepository = new UpdateEmployerRepository();
    const updateEmployerUsecase = new UpdateEmployerUsecase(
      updateEmployerRepository
    );
    const result = await updateEmployerUsecase.execute(id, data);
    return result;
  } catch (error) {
    const formatedError = error as PostgrestError;

    /* NOTE: Dans le cas ou on a besoin de gerer une autre code d'erreur
     * avec des messages personnalisees, on peut l'ajouter ici
     */
    switch (formatedError.code) {
      case "23505":
        return rejectWithValue("Matricule deja existant.");
      default:
        return rejectWithValue(formatedError.message);
    }
  }
});

export const deleteEmployerThunk = createAsyncThunk(
  "employer/delete",
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteEmployerRepository = new DeleteEmployerRepository();
      const deleteEmployerUsecase = new DeleteEmployerUsecase(
        deleteEmployerRepository
      );
      await deleteEmployerUsecase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const employerSlice = createSlice({
  name: "employer",
  initialState,
  reducers: {
    setSelectedEmployer: (setSelectedEmployerSlice, action) => {
      setSelectedEmployerSlice.selectedEmplyerSlice = action.payload;
    },
    clearSelectedEmployer: (state) => {
      state.selectedEmplyerSlice = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createEmployerThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createEmployerThunk.fulfilled, (state, action) => {
        state.employers.push(action.payload);
        state.error = null;
        state.loading = false;
      })
      .addCase(createEmployerThunk.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      })
      // Get employer
      .addCase(getEmployersByProfessionalIdThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getEmployersByProfessionalIdThunk.fulfilled, (state, action) => {
        state.employers = action.payload;
        state.error = null;
        state.loading = false;
      })
      .addCase(getEmployersByProfessionalIdThunk.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      })
      // Get employer
      .addCase(getEmployerByIdThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getEmployerByIdThunk.fulfilled, (state, action) => {
        state.selectedEmplyerSlice = action.payload;
        state.error = null;
        state.loading = false;
      })
      .addCase(getEmployerByIdThunk.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      })
      // Update
      .addCase(updateEmployerThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateEmployerThunk.fulfilled, (state, action) => {
        const index = state.employers.findIndex(
          (am) => am.id === action.payload.id
        );
        if (index !== -1) {
          state.employers[index] = action.payload;
        }
        state.error = null;
        state.loading = false;
      })
      .addCase(updateEmployerThunk.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      })
      // Delete
      .addCase(deleteEmployerThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteEmployerThunk.fulfilled, (state, action) => {
        state.employers = state.employers.filter(
          (am) => am.id !== Number(action.payload)
        );
        state.error = null;
        state.loading = false;
      })
      .addCase(deleteEmployerThunk.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      });
  },
});

export const { setSelectedEmployer, clearSelectedEmployer } =
  employerSlice.actions;
export default employerSlice.reducer;
