# Étape 1 : Builder l'application avec les variables d'environnement
FROM node:20 AS build

WORKDIR /app

# Copier les fichiers de dépendances
COPY package*.json ./
RUN npm install

# Copier le code source
COPY . .

# C<PERSON>er le fichier .env à partir des arguments de build
ARG VITE_SUPABASE_URL
ARG VITE_SUPABASE_ANON_KEY
ARG BREVO_API_KEY
ARG VITE_APP_URL

# Créer le fichier .env avec les variables
RUN echo "VITE_SUPABASE_URL=$VITE_SUPABASE_URL" > .env && \
    echo "VITE_SUPABASE_ANON_KEY=$VITE_SUPABASE_ANON_KEY" >> .env && \
    echo "BREVO_API_KEY=$BREVO_API_KEY" >> .env && \
    echo "VITE_APP_URL=$VITE_APP_URL" >> .env

# Builder l'application
RUN npm run build

# Étape 2 : Servir avec Nginx
FROM nginx:alpine

# Copier le build depuis l'étape précédente
COPY --from=build /app/dist /usr/share/nginx/html

# Supprime la config par défaut de Nginx (optionnel)
RUN rm /etc/nginx/conf.d/default.conf

# Ajoute ta propre config Nginx si besoin
COPY nginx.conf /etc/nginx/conf.d

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
