name: Deploy React App to VPS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy to VPS via SSH & Docker
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          # C<PERSON>er la clé SSH avec un format correct
          echo "${{ secrets.VPS_SSH_KEY }}" | base64 -d > ~/.ssh/id_rsa || echo "${{ secrets.VPS_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          chmod 700 ~/.ssh
          # Vérifier le format de la clé
          ssh-keygen -l -f ~/.ssh/id_rsa
          # Ajouter l'hôte aux known_hosts
          ssh-keyscan -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts
          # Test de connexion
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} "echo 'SSH connection test successful'"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build React App
        env:
          VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}
          BREVO_API_KEY: ${{ secrets.BREVO_API_KEY }}
          VITE_APP_URL: https://app.meddoc.mg
        run: |
          # Vérifier que les variables d'environnement sont définies
          echo "=== Vérification des variables d'environnement ==="
          echo "VITE_SUPABASE_URL: ${VITE_SUPABASE_URL:0:30}..."
          echo "VITE_SUPABASE_ANON_KEY: ${VITE_SUPABASE_ANON_KEY:0:30}..."
          echo "BREVO_API_KEY: ${BREVO_API_KEY:0:30}..."
          echo "VITE_APP_URL: ${VITE_APP_URL}"

          if [ -z "$VITE_SUPABASE_URL" ] || [ -z "$VITE_SUPABASE_ANON_KEY" ] || [ -z "$BREVO_API_KEY" ] || [ -z "$VITE_APP_URL" ]; then
            echo "❌ Variables d'environnement manquantes !"
            echo "VITE_SUPABASE_URL définie: $([ -n "$VITE_SUPABASE_URL" ] && echo "OUI" || echo "NON")"
            echo "VITE_SUPABASE_ANON_KEY définie: $([ -n "$VITE_SUPABASE_ANON_KEY" ] && echo "OUI" || echo "NON")"
            echo "BREVO_API_KEY définie: $([ -n "$BREVO_API_KEY" ] && echo "OUI" || echo "NON")"
            echo "VITE_APP_URL définie: $([ -n "$VITE_APP_URL" ] && echo "OUI" || echo "NON")"
            exit 1
          fi

          # Créer le fichier .env
          echo "=== Création du fichier .env ==="
          echo "VITE_SUPABASE_URL=$VITE_SUPABASE_URL" > .env
          echo "VITE_SUPABASE_ANON_KEY=$VITE_SUPABASE_ANON_KEY" >> .env
          echo "BREVO_API_KEY=$BREVO_API_KEY" >> .env
          echo "VITE_APP_URL=$VITE_APP_URL" >> .env

          echo "✅ Fichier .env créé avec $(wc -l < .env) lignes"

          # Vérifier le contenu (masqué pour la sécurité)
          echo "Première ligne du .env: $(head -1 .env | cut -c1-30)..."

          # Build de l'application avec les variables explicites
          echo "=== Lancement du build ==="
          VITE_SUPABASE_URL="$VITE_SUPABASE_URL" VITE_SUPABASE_ANON_KEY="$VITE_SUPABASE_ANON_KEY" BREVO_API_KEY="$BREVO_API_KEY" VITE_APP_URL="$VITE_APP_URL" npm run build

          # Vérifier le build
          echo "=== Vérification du build ==="
          ls -la dist/
          echo "Fichiers JS générés:"
          find dist/ -name "*.js" | head -5

          # Chercher les variables Supabase dans le build
          echo "=== Recherche des variables Supabase ==="
          if find dist/ -name "*.js" -exec grep -l "supabase\.co" {} \; | head -1; then
            echo "✅ URL Supabase trouvée dans le build"
          else
            echo "⚠️ URL Supabase non trouvée dans le build"
          fi

      - name: Prepare VPS directory
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          echo "Current user: $(whoami)"
          echo "Home directory: $HOME"
          echo "Current directory: $(pwd)"
          mkdir -p $HOME/app-meddoc
          ls -la $HOME/
          echo "Directory created successfully"
          EOF

      - name: Copy files to VPS
        run: |
          # Créer un répertoire temporaire
          mkdir -p temp_deploy

          # Copier TOUT le contenu du dépôt (sauf ce qu'on veut exclure)
          find . -maxdepth 1 -not -name '.' -not -name 'temp_deploy' -not -name '.git' -not -name 'node_modules' -exec cp -r {} temp_deploy/ \;

          # Vérifier ce qui a été copié
          echo "Contenu copié dans temp_deploy:"
          ls -la temp_deploy/

          # Vérifier que le dossier dist est présent avec le build
          echo "Contenu du dossier dist:"
          ls -la temp_deploy/dist/ || echo "ATTENTION: dossier dist non trouvé"

          # Vérifier que index.html est bien présent
          echo "Vérification du fichier index.html:"
          ls -la temp_deploy/public/index.html || echo "ERREUR: index.html non trouvé"

          # Créer l'archive
          tar -czf deploy.tar.gz -C temp_deploy .

          # Transférer l'archive
          scp -i ~/.ssh/id_rsa deploy.tar.gz ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }}:~/

          # Extraire sur le VPS
          ssh -i ~/.ssh/id_rsa ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
          rm -rf ~/app-meddoc
          mkdir -p ~/app-meddoc
          tar -xzf ~/deploy.tar.gz -C ~/app-meddoc
          rm ~/deploy.tar.gz
          echo "Contenu du dossier après extraction:"
          ls -la ~/app-meddoc/public
          EOF

          # Nettoyer localement
          rm -rf temp_deploy deploy.tar.gz

      - name: Build and deploy on VPS
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << EOF
          cd ~/app-meddoc

          # Créer le fichier .env pour Docker Compose
          cat > .env << 'ENVEOF'
          VITE_SUPABASE_URL=${{ secrets.VITE_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY=${{ secrets.VITE_SUPABASE_ANON_KEY }}
          BREVO_API_KEY=${{ secrets.BREVO_API_KEY }}
          VITE_APP_URL=https://app.meddoc.mg
          ENVEOF

          echo "Variables d'environnement pour Docker:"
          cat .env

          # Arrêter les conteneurs existants
          docker compose down || true

          # Supprimer les images pour forcer le rebuild
          docker compose build --no-cache

          # Démarrer les nouveaux conteneurs
          docker compose up -d

          # Vérifier le statut
          docker compose ps
          docker compose logs frontend --tail=20
          EOF

